import { BaseStrategy, StrategyConfig, MarketData, TradeDecision } from '../../src/types'

// Base Strategy Abstract Class
export abstract class BaseStrategyImpl implements BaseStrategy {
  public name: string
  public description: string
  public config: StrategyConfig
  
  protected consecutiveLosses: number = 0
  protected consecutiveWins: number = 0
  protected totalTrades: number = 0
  protected currentAmount: number

  constructor(name: string, description: string, config: StrategyConfig) {
    this.name = name
    this.description = description
    this.config = config
    this.currentAmount = config.baseAmount || config.amount || 1
  }

  abstract execute(marketData: MarketData): Promise<TradeDecision>
  
  calculateConfidence(marketData: MarketData): number {
    let confidence = 0

    // RSI-based confidence (30% weight)
    if (marketData.rsi <= 30) {
      confidence += 30 // Oversold, likely to go up
    } else if (marketData.rsi >= 70) {
      confidence += 30 // Overbought, likely to go down
    } else if (marketData.rsi >= 40 && marketData.rsi <= 60) {
      confidence += 15 // Neutral zone
    }

    // EMA trend confidence (25% weight)
    if (marketData.emaShort > marketData.emaLong) {
      confidence += 25 // Uptrend
    } else if (marketData.emaShort < marketData.emaLong) {
      confidence += 25 // Downtrend
    }

    // Price trend confidence (25% weight)
    if (marketData.trend === 'up' || marketData.trend === 'down') {
      confidence += 25 // Clear trend
    } else {
      confidence += 10 // Sideways trend
    }

    // Volatility confidence (20% weight)
    if (marketData.volatility < 0.02) {
      confidence += 20 // Low volatility, more predictable
    } else if (marketData.volatility < 0.05) {
      confidence += 15 // Medium volatility
    } else {
      confidence += 5 // High volatility, less predictable
    }

    return Math.min(confidence, 100)
  }

  reset(): void {
    this.consecutiveLosses = 0
    this.consecutiveWins = 0
    this.totalTrades = 0
    this.currentAmount = this.config.baseAmount || this.config.amount || 1
  }

  protected updateStats(won: boolean): void {
    this.totalTrades++
    
    if (won) {
      this.consecutiveWins++
      this.consecutiveLosses = 0
    } else {
      this.consecutiveLosses++
      this.consecutiveWins = 0
    }
  }

  protected determineDirection(marketData: MarketData): 'high' | 'low' {
    let score = 0

    // RSI signals
    if (marketData.rsi <= 30) {
      score += 2 // Strong buy signal
    } else if (marketData.rsi <= 40) {
      score += 1 // Weak buy signal
    } else if (marketData.rsi >= 70) {
      score -= 2 // Strong sell signal
    } else if (marketData.rsi >= 60) {
      score -= 1 // Weak sell signal
    }

    // EMA signals
    if (marketData.emaShort > marketData.emaLong) {
      score += 1 // Uptrend
    } else {
      score -= 1 // Downtrend
    }

    // Trend signals
    if (marketData.trend === 'up') {
      score += 1
    } else if (marketData.trend === 'down') {
      score -= 1
    }

    return score > 0 ? 'high' : 'low'
  }
}
