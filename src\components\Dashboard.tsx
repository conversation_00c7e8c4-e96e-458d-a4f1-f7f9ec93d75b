import React from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Activity,
  Clock,
  BarChart3,
  Zap
} from 'lucide-react'
import { useBotStore, useWinRate, useTotalProfit, useLatestTrades } from '../store/useBotStore'
import { MetricCard } from './MetricCard'
import { TradeItem } from './TradeItem'
import { ConfidenceIndicator } from './ConfidenceIndicator'
import { clsx } from 'clsx'

export const Dashboard: React.FC = () => {
  const { 
    currentSession, 
    marketData, 
    selectedAsset, 
    botStatus,
    config
  } = useBotStore()
  
  const winRate = useWinRate()
  const totalProfit = useTotalProfit()
  const latestTrades = useLatestTrades(5)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getSessionDuration = () => {
    if (!currentSession) return '0m'
    
    const duration = Date.now() - currentSession.startTime
    const minutes = Math.floor(duration / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  return (
    <div className="h-full overflow-y-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Trading Dashboard</h1>
          <p className="text-dark-400">
            Monitor your bot's performance and market data
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          {selectedAsset && (
            <div className="text-right">
              <div className="text-sm text-dark-400">Selected Asset</div>
              <div className="font-semibold">{selectedAsset.name}</div>
            </div>
          )}
          
          <div className={clsx(
            'px-3 py-1 rounded-full text-sm font-medium',
            botStatus === 'running' && 'bg-success-900 text-success-100',
            botStatus === 'stopped' && 'bg-dark-700 text-dark-300',
            botStatus === 'error' && 'bg-danger-900 text-danger-100'
          )}>
            {botStatus.charAt(0).toUpperCase() + botStatus.slice(1)}
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Profit"
          value={formatCurrency(totalProfit)}
          icon={DollarSign}
          trend={totalProfit >= 0 ? 'up' : 'down'}
          trendValue={totalProfit >= 0 ? `+${formatCurrency(totalProfit)}` : formatCurrency(totalProfit)}
        />
        
        <MetricCard
          title="Win Rate"
          value={formatPercentage(winRate)}
          icon={Target}
          trend={winRate >= 50 ? 'up' : 'down'}
          trendValue={`${currentSession?.winningTrades || 0}/${currentSession?.totalTrades || 0} trades`}
        />
        
        <MetricCard
          title="Session Duration"
          value={getSessionDuration()}
          icon={Clock}
          trend="neutral"
          trendValue={`${currentSession?.totalTrades || 0} trades executed`}
        />
        
        <MetricCard
          title="Current Streak"
          value={`${Math.abs(currentSession?.currentStreak || 0)}`}
          icon={currentSession?.currentStreak && currentSession.currentStreak > 0 ? TrendingUp : TrendingDown}
          trend={currentSession?.currentStreak && currentSession.currentStreak > 0 ? 'up' : 'down'}
          trendValue={currentSession?.currentStreak && currentSession.currentStreak > 0 ? 'Winning' : 'Losing'}
        />
      </div>

      {/* Market Data & Confidence */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Market Data */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Market Data
            </h3>
            <p className="card-description">
              Real-time price and technical indicators
            </p>
          </div>
          
          <div className="card-content space-y-4">
            {marketData ? (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-dark-400">Current Price</div>
                    <div className="text-xl font-bold">
                      {marketData.price.toFixed(5)}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-dark-400">Trend</div>
                    <div className={clsx(
                      'text-lg font-semibold capitalize',
                      marketData.trend === 'up' && 'text-success-400',
                      marketData.trend === 'down' && 'text-danger-400',
                      marketData.trend === 'sideways' && 'text-yellow-400'
                    )}>
                      {marketData.trend}
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="text-dark-400">RSI</div>
                    <div className="font-medium">{marketData.rsi.toFixed(1)}</div>
                  </div>
                  
                  <div>
                    <div className="text-dark-400">EMA Short</div>
                    <div className="font-medium">{marketData.emaShort.toFixed(5)}</div>
                  </div>
                  
                  <div>
                    <div className="text-dark-400">EMA Long</div>
                    <div className="font-medium">{marketData.emaLong.toFixed(5)}</div>
                  </div>
                </div>
                
                <div>
                  <div className="text-sm text-dark-400 mb-2">Volatility</div>
                  <div className="w-full bg-dark-700 rounded-full h-2">
                    <div 
                      className={clsx(
                        'h-2 rounded-full transition-all duration-300',
                        marketData.volatility < 0.02 ? 'bg-success-500' :
                        marketData.volatility < 0.05 ? 'bg-yellow-500' : 'bg-danger-500'
                      )}
                      style={{ width: `${Math.min(marketData.volatility * 1000, 100)}%` }}
                    />
                  </div>
                  <div className="text-xs text-dark-400 mt-1">
                    {(marketData.volatility * 100).toFixed(2)}%
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-dark-400">
                <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No market data available</p>
                <p className="text-xs">Start the bot to begin analysis</p>
              </div>
            )}
          </div>
        </div>

        {/* Confidence & Strategy */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Strategy & Confidence
            </h3>
            <p className="card-description">
              Current strategy performance and confidence level
            </p>
          </div>
          
          <div className="card-content space-y-4">
            <div>
              <div className="text-sm text-dark-400">Active Strategy</div>
              <div className="text-lg font-semibold capitalize">
                {config.strategy.replace('-', ' ')}
              </div>
            </div>
            
            {marketData && (
              <ConfidenceIndicator 
                confidence={marketData.confidence}
                threshold={config.confidenceThreshold}
              />
            )}
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-dark-400">Trade Amount</div>
                <div className="font-medium">{formatCurrency(config.tradeAmount)}</div>
              </div>
              
              <div>
                <div className="text-dark-400">Time Period</div>
                <div className="font-medium">{config.timePeriod}</div>
              </div>
            </div>
            
            <div className="flex items-center gap-2 text-sm">
              <div className={clsx(
                'px-2 py-1 rounded text-xs',
                config.dryRun ? 'bg-yellow-900 text-yellow-100' : 'bg-success-900 text-success-100'
              )}>
                {config.dryRun ? 'Dry Run' : 'Live Trading'}
              </div>
              
              <div className={clsx(
                'px-2 py-1 rounded text-xs',
                config.accountType === 'demo' ? 'bg-blue-900 text-blue-100' : 'bg-danger-900 text-danger-100'
              )}>
                {config.accountType.toUpperCase()}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Trades */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">Recent Trades</h3>
          <p className="card-description">
            Latest trading activity and results
          </p>
        </div>
        
        <div className="card-content">
          {latestTrades.length > 0 ? (
            <div className="space-y-2">
              {latestTrades.map((trade) => (
                <TradeItem key={trade.id} trade={trade} />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-dark-400">
              <Activity className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No trades yet</p>
              <p className="text-xs">Trades will appear here once the bot starts</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
