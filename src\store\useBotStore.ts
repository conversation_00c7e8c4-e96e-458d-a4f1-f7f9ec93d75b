import { create } from 'zustand'
import { AppState, BotConfig, Trade, TradingSession, Asset, MarketData, Notification } from '../types'

interface BotStore extends AppState {
  // Actions
  setConnected: (connected: boolean) => void
  setAuthenticated: (authenticated: boolean) => void
  setBotStatus: (status: AppState['botStatus']) => void
  setCurrentSession: (session: TradingSession | undefined) => void
  addTrade: (trade: Trade) => void
  updateTrade: (tradeId: string, updates: Partial<Trade>) => void
  setAvailableAssets: (assets: Asset[]) => void
  setSelectedAsset: (asset: Asset | undefined) => void
  setMarketData: (data: MarketData | undefined) => void
  setError: (error: string | undefined) => void
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
  
  // Bot configuration
  config: BotConfig
  updateConfig: (updates: Partial<BotConfig>) => void
  
  // UI state
  activeTab: string
  setActiveTab: (tab: string) => void
  sidebarCollapsed: boolean
  setSidebarCollapsed: (collapsed: boolean) => void
}

const defaultConfig: BotConfig = {
  accountType: 'demo',
  strategyMode: 'oscillate',
  tradeCapital: 100,
  targetProfit: 20,
  tradeAmount: 1,
  strategy: 'fixed-rsi',
  assetFilter: {
    type: 'OTC',
    category: 'currency',
    selectedAssets: []
  },
  timePeriod: 'M1',
  confidenceThreshold: 87,
  dryRun: true,
  debugLogs: false
}

export const useBotStore = create<BotStore>((set, get) => ({
  // Initial state
  isConnected: false,
  isAuthenticated: false,
  botStatus: 'stopped',
  currentSession: undefined,
  recentTrades: [],
  availableAssets: [],
  selectedAsset: undefined,
  marketData: undefined,
  error: undefined,
  notifications: [],
  config: defaultConfig,
  activeTab: 'dashboard',
  sidebarCollapsed: false,

  // Actions
  setConnected: (connected) => set({ isConnected: connected }),
  
  setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),
  
  setBotStatus: (status) => set({ botStatus: status }),
  
  setCurrentSession: (session) => set({ currentSession: session }),
  
  addTrade: (trade) => set((state) => ({
    recentTrades: [trade, ...state.recentTrades].slice(0, 50) // Keep last 50 trades
  })),
  
  updateTrade: (tradeId, updates) => set((state) => ({
    recentTrades: state.recentTrades.map(trade =>
      trade.id === tradeId ? { ...trade, ...updates } : trade
    )
  })),
  
  setAvailableAssets: (assets) => set({ availableAssets: assets }),
  
  setSelectedAsset: (asset) => set({ selectedAsset: asset }),
  
  setMarketData: (data) => set({ marketData: data }),
  
  setError: (error) => set({ error }),
  
  addNotification: (notification) => {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: Date.now()
    }
    
    set((state) => ({
      notifications: [newNotification, ...state.notifications].slice(0, 10) // Keep last 10 notifications
    }))
    
    // Auto-remove notification after 5 seconds if autoClose is true
    if (notification.autoClose !== false) {
      setTimeout(() => {
        get().removeNotification(id)
      }, 5000)
    }
  },
  
  removeNotification: (id) => set((state) => ({
    notifications: state.notifications.filter(n => n.id !== id)
  })),
  
  clearNotifications: () => set({ notifications: [] }),
  
  updateConfig: (updates) => set((state) => ({
    config: { ...state.config, ...updates }
  })),
  
  setActiveTab: (tab) => set({ activeTab: tab }),
  
  setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed })
}))

// Selectors
export const useIsRunning = () => useBotStore(state => state.botStatus === 'running')
export const useCanStart = () => useBotStore(state => 
  state.isAuthenticated && 
  state.botStatus === 'stopped' && 
  state.config.assetFilter.selectedAssets.length > 0
)
export const useWinRate = () => useBotStore(state => {
  const session = state.currentSession
  if (!session || session.totalTrades === 0) return 0
  return (session.winningTrades / session.totalTrades) * 100
})
export const useTotalProfit = () => useBotStore(state => 
  state.currentSession?.totalProfit || 0
)
export const useRecentTradesCount = () => useBotStore(state => state.recentTrades.length)
export const useLatestTrades = (count: number = 10) => useBotStore(state => 
  state.recentTrades.slice(0, count)
)
