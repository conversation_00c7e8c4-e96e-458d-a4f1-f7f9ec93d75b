import React, { useEffect } from 'react'
import { useBotStore } from './store/useBotStore'
import { Sidebar } from './components/Sidebar'
import { Dashboard } from './components/Dashboard'
import { Configuration } from './components/Configuration'
import { TradeHistory } from './components/TradeHistory'
import { Authentication } from './components/Authentication'
import { NotificationContainer } from './components/NotificationContainer'
import { StatusBar } from './components/StatusBar'

function App() {
  const { 
    activeTab, 
    isAuthenticated, 
    setBotStatus, 
    setConnected,
    addNotification,
    addTrade,
    updateTrade,
    setCurrentSession,
    setMarketData,
    setAvailableAssets
  } = useBotStore()

  useEffect(() => {
    // Check if Electron API is available
    if (window.electronAPI) {
      setConnected(true)
      
      // Set up event listeners for bot events
      const handleStatusChange = (event: any, status: string, data?: any) => {
        setBotStatus(status as any)
        if (data) {
          setCurrentSession(data.currentSession)
        }
      }

      const handleTradeOpened = (event: any, trade: any) => {
        addTrade(trade)
        addNotification({
          type: 'info',
          title: 'Trade Opened',
          message: `${trade.direction.toUpperCase()} ${trade.asset} - $${trade.amount}`,
          autoClose: true
        })
      }

      const handleTradeClosed = (event: any, trade: any) => {
        updateTrade(trade.id, trade)
        const isWin = trade.result === 'win'
        addNotification({
          type: isWin ? 'success' : 'error',
          title: `Trade ${isWin ? 'Won' : 'Lost'}`,
          message: `${trade.asset} - ${isWin ? '+' : ''}$${trade.profit?.toFixed(2)}`,
          autoClose: true
        })
      }

      const handleMarketData = (event: any, data: any) => {
        setMarketData(data)
      }

      const handleAuthenticated = (event: any, data: any) => {
        if (data.assets) {
          setAvailableAssets(data.assets)
        }
        addNotification({
          type: 'success',
          title: 'Authentication Successful',
          message: 'Connected to Pocket Option',
          autoClose: true
        })
      }

      const handleError = (event: any, error: any) => {
        addNotification({
          type: 'error',
          title: 'Bot Error',
          message: error.message || 'An error occurred',
          autoClose: false
        })
      }

      // Register event listeners
      window.electronAPI.on('bot:status_changed', handleStatusChange)
      window.electronAPI.on('bot:trade_opened', handleTradeOpened)
      window.electronAPI.on('bot:trade_closed', handleTradeClosed)
      window.electronAPI.on('bot:market_data', handleMarketData)
      window.electronAPI.on('bot:authenticated', handleAuthenticated)
      window.electronAPI.on('bot:error', handleError)

      // Cleanup function
      return () => {
        window.electronAPI.removeAllListeners('bot:status_changed')
        window.electronAPI.removeAllListeners('bot:trade_opened')
        window.electronAPI.removeAllListeners('bot:trade_closed')
        window.electronAPI.removeAllListeners('bot:market_data')
        window.electronAPI.removeAllListeners('bot:authenticated')
        window.electronAPI.removeAllListeners('bot:error')
      }
    } else {
      // Running in web mode (development)
      setConnected(false)
      addNotification({
        type: 'warning',
        title: 'Development Mode',
        message: 'Running in web mode. Bot functionality is limited.',
        autoClose: false
      })
    }
  }, [])

  const renderActiveTab = () => {
    if (!isAuthenticated && activeTab !== 'auth') {
      return <Authentication />
    }

    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />
      case 'config':
        return <Configuration />
      case 'history':
        return <TradeHistory />
      case 'auth':
        return <Authentication />
      default:
        return <Dashboard />
    }
  }

  return (
    <div className="flex h-screen bg-dark-950 text-dark-50">
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Status Bar */}
        <StatusBar />
        
        {/* Content Area */}
        <main className="flex-1 overflow-hidden p-4">
          {renderActiveTab()}
        </main>
      </div>
      
      {/* Notifications */}
      <NotificationContainer />
    </div>
  )
}

export default App
