import React, { useState } from 'react'
import { 
  History, 
  Filter, 
  Download, 
  TrendingUp, 
  TrendingDown,
  Search,
  Calendar
} from 'lucide-react'
import { useBotStore } from '../store/useBotStore'
import { TradeItem } from './TradeItem'
import { Trade } from '../types'
import { clsx } from 'clsx'

export const TradeHistory: React.FC = () => {
  const { recentTrades, currentSession } = useBotStore()
  const [filter, setFilter] = useState<'all' | 'win' | 'loss' | 'pending'>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'time' | 'profit' | 'asset'>('time')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  const filteredTrades = recentTrades
    .filter(trade => {
      // Filter by result
      if (filter !== 'all' && trade.result !== filter) {
        return false
      }
      
      // Filter by search term
      if (searchTerm && !trade.asset.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false
      }
      
      return true
    })
    .sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'time':
          comparison = a.openTime - b.openTime
          break
        case 'profit':
          comparison = (a.profit || 0) - (b.profit || 0)
          break
        case 'asset':
          comparison = a.asset.localeCompare(b.asset)
          break
      }
      
      return sortOrder === 'asc' ? comparison : -comparison
    })

  const getFilterStats = () => {
    const total = recentTrades.length
    const wins = recentTrades.filter(t => t.result === 'win').length
    const losses = recentTrades.filter(t => t.result === 'loss').length
    const pending = recentTrades.filter(t => t.result === 'pending').length
    
    return { total, wins, losses, pending }
  }

  const stats = getFilterStats()

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const exportTrades = () => {
    const csvContent = [
      ['Time', 'Asset', 'Direction', 'Amount', 'Result', 'Profit', 'Strategy', 'Confidence'].join(','),
      ...filteredTrades.map(trade => [
        new Date(trade.openTime).toISOString(),
        trade.asset,
        trade.direction,
        trade.amount,
        trade.result || 'pending',
        trade.profit || 0,
        trade.strategy,
        trade.confidence || 0
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `trades_${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <div className="h-full overflow-y-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <History className="h-6 w-6" />
            Trade History
          </h1>
          <p className="text-dark-400">
            Review your trading performance and results
          </p>
        </div>
        
        <button
          onClick={exportTrades}
          disabled={filteredTrades.length === 0}
          className="btn-secondary flex items-center gap-2 disabled:opacity-50"
        >
          <Download className="h-4 w-4" />
          Export CSV
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="card p-4">
          <div className="text-center">
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-sm text-dark-400">Total Trades</div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-success-400">{stats.wins}</div>
            <div className="text-sm text-dark-400">Wins</div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-danger-400">{stats.losses}</div>
            <div className="text-sm text-dark-400">Losses</div>
          </div>
        </div>
        
        <div className="card p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">{stats.pending}</div>
            <div className="text-sm text-dark-400">Pending</div>
          </div>
        </div>
      </div>

      {/* Session Summary */}
      {currentSession && (
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Current Session</h3>
            <p className="card-description">
              Session statistics and performance metrics
            </p>
          </div>
          
          <div className="card-content">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-dark-400">Total Profit</div>
                <div className={clsx(
                  'text-lg font-bold',
                  currentSession.totalProfit >= 0 ? 'text-success-400' : 'text-danger-400'
                )}>
                  {formatCurrency(currentSession.totalProfit)}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-dark-400">Win Rate</div>
                <div className="text-lg font-bold">
                  {currentSession.totalTrades > 0 
                    ? ((currentSession.winningTrades / currentSession.totalTrades) * 100).toFixed(1)
                    : 0}%
                </div>
              </div>
              
              <div>
                <div className="text-sm text-dark-400">Best Streak</div>
                <div className="text-lg font-bold text-success-400">
                  {currentSession.bestStreak}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-dark-400">Worst Streak</div>
                <div className="text-lg font-bold text-danger-400">
                  {Math.abs(currentSession.worstStreak)}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      <div className="card">
        <div className="card-content">
          <div className="flex flex-wrap items-center gap-4">
            {/* Search */}
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-dark-400" />
                <input
                  type="text"
                  placeholder="Search by asset..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input pl-10"
                />
              </div>
            </div>
            
            {/* Filter by result */}
            <div className="flex gap-2">
              {(['all', 'win', 'loss', 'pending'] as const).map(filterOption => (
                <button
                  key={filterOption}
                  onClick={() => setFilter(filterOption)}
                  className={clsx(
                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                    filter === filterOption
                      ? 'bg-primary-600 text-white'
                      : 'bg-dark-700 text-dark-300 hover:bg-dark-600'
                  )}
                >
                  {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
                </button>
              ))}
            </div>
            
            {/* Sort options */}
            <div className="flex gap-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="select text-sm"
              >
                <option value="time">Sort by Time</option>
                <option value="profit">Sort by Profit</option>
                <option value="asset">Sort by Asset</option>
              </select>
              
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="btn-secondary px-3"
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Trade List */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">
            Trades ({filteredTrades.length})
          </h3>
          <p className="card-description">
            Detailed view of individual trades
          </p>
        </div>
        
        <div className="card-content">
          {filteredTrades.length > 0 ? (
            <div className="space-y-3">
              {filteredTrades.map((trade) => (
                <TradeItem 
                  key={trade.id} 
                  trade={trade} 
                  showDetails={true}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 text-dark-400">
              <History className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No trades found</h3>
              <p className="text-sm">
                {searchTerm || filter !== 'all' 
                  ? 'Try adjusting your filters or search term'
                  : 'Start trading to see your history here'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
