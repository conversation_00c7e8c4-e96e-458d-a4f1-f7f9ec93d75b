import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Settings, DollarSign, Target, TrendingUp, Clock, Filter, Zap, Save, RotateCcw, Camera } from 'lucide-react'
import { useBotStore } from '../store/useBotStore'
import { BotConfig, StrategyType, TimePeriod } from '../types'
import { clsx } from 'clsx'

export const Configuration: React.FC = () => {
	const { config, updateConfig, availableAssets, addNotification } = useBotStore()
	const [availableStrategies, setAvailableStrategies] = useState<any[]>([])

	const { register, handleSubmit, watch, setValue, reset } = useForm<BotConfig>({
		defaultValues: config
	})

	// Load available strategies on component mount
	useEffect(() => {
		const loadStrategies = async () => {
			if (window.electronAPI) {
				try {
					const result = await window.electronAPI.strategy.getAvailable()
					if (result.success) {
						setAvailableStrategies(result.strategies)
					}
				} catch (error) {
					console.error('Failed to load strategies:', error)
				}
			}
		}

		loadStrategies()
	}, [])

	// Reset form when config changes
	useEffect(() => {
		reset(config)
	}, [config, reset])

	const onSubmit = (data: BotConfig) => {
		updateConfig(data)
		addNotification({
			type: 'success',
			title: 'Configuration Saved',
			message: 'Bot configuration has been updated',
			autoClose: true
		})
	}

	const handleResetToDefaults = () => {
		const defaultConfig: BotConfig = {
			accountType: 'demo',
			strategyMode: 'oscillate',
			tradeCapital: 100,
			targetProfit: 20,
			tradeAmount: 1,
			strategy: 'fixed-rsi',
			assetFilter: {
				type: 'OTC',
				category: 'currency',
				selectedAssets: []
			},
			timePeriod: 'M1',
			confidenceThreshold: 87,
			dryRun: true,
			debugLogs: false,
			headlessBrowser: true
		}

		updateConfig(defaultConfig)
		addNotification({
			type: 'info',
			title: 'Reset to Defaults',
			message: 'Configuration has been reset to default values',
			autoClose: true
		})
	}

	const handleSelectScreenshotArea = async () => {
		if (!window.electronAPI) {
			addNotification({
				type: 'error',
				title: 'Not Available',
				message: 'Screenshot selection is only available in Electron mode',
				autoClose: true
			})
			return
		}

		try {
			const result = await window.electronAPI.dialog.selectScreenshotArea()
			if (result.success) {
				addNotification({
					type: 'success',
					title: 'Screenshot Area Selected',
					message: 'Chart coordinates have been captured',
					autoClose: true
				})
			} else {
				addNotification({
					type: 'error',
					title: 'Selection Failed',
					message: result.message || 'Failed to select screenshot area',
					autoClose: true
				})
			}
		} catch (error) {
			addNotification({
				type: 'error',
				title: 'Error',
				message: 'Failed to select screenshot area',
				autoClose: true
			})
		}
	}

	const strategyOptions: { value: StrategyType; label: string }[] = [
		{ value: 'martingale', label: 'Martingale' },
		{ value: 'dalembert', label: "D'Alembert" },
		{ value: 'fixed-rsi', label: 'Fixed RSI' }
	]

	const timePeriodOptions: { value: TimePeriod; label: string }[] = [
		{ value: 'S5', label: '5 Seconds' },
		{ value: 'S15', label: '15 Seconds' },
		{ value: 'S30', label: '30 Seconds' },
		{ value: 'M1', label: '1 Minute' },
		{ value: 'M3', label: '3 Minutes' },
		{ value: 'M5', label: '5 Minutes' },
		{ value: 'M15', label: '15 Minutes' },
		{ value: 'M30', label: '30 Minutes' },
		{ value: 'H1', label: '1 Hour' }
	]

	const categoryOptions = [
		{ value: 'currency', label: 'Currency' },
		{ value: 'crypto', label: 'Cryptocurrency' },
		{ value: 'commodities', label: 'Commodities' },
		{ value: 'stocks', label: 'Stocks' },
		{ value: 'indices', label: 'Indices' }
	]

	return (
		<div className="h-full overflow-y-auto">
			<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
				{/* Header */}
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-2xl font-bold flex items-center gap-2">
							<Settings className="h-6 w-6" />
							Bot Configuration
						</h1>
						<p className="text-dark-400">Configure your trading bot settings and strategy</p>
					</div>

					<div className="flex gap-2">
						<button type="button" onClick={handleResetToDefaults} className="btn-secondary flex items-center gap-2">
							<RotateCcw className="h-4 w-4" />
							Reset
						</button>

						<button type="submit" className="btn-primary flex items-center gap-2">
							<Save className="h-4 w-4" />
							Save Configuration
						</button>
					</div>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* Basic Settings */}
					<div className="card">
						<div className="card-header">
							<h3 className="card-title flex items-center gap-2">
								<DollarSign className="h-5 w-5" />
								Basic Settings
							</h3>
							<p className="card-description">Core trading parameters and account settings</p>
						</div>

						<div className="card-content space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div className="form-group">
									<label className="form-label">Account Type</label>
									<select {...register('accountType')} className="select">
										<option value="demo">Demo</option>
										<option value="live">Live</option>
									</select>
								</div>

								<div className="form-group">
									<label className="form-label">Strategy Mode</label>
									<select {...register('strategyMode')} className="select">
										<option value="oscillate">Oscillate</option>
										<option value="slide">Slide</option>
									</select>
								</div>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div className="form-group">
									<label className="form-label">Trade Capital ($)</label>
									<input
										type="number"
										step="0.01"
										min="1"
										{...register('tradeCapital', { valueAsNumber: true })}
										className="input"
									/>
								</div>

								<div className="form-group">
									<label className="form-label">Target Profit ($)</label>
									<input
										type="number"
										step="0.01"
										min="1"
										{...register('targetProfit', { valueAsNumber: true })}
										className="input"
									/>
								</div>
							</div>

							<div className="form-group">
								<label className="form-label">Trade Amount ($)</label>
								<input
									type="number"
									step="0.01"
									min="1"
									{...register('tradeAmount', { valueAsNumber: true })}
									className="input"
								/>
							</div>
						</div>
					</div>

					{/* Strategy Settings */}
					<div className="card">
						<div className="card-header">
							<h3 className="card-title flex items-center gap-2">
								<TrendingUp className="h-5 w-5" />
								Strategy Settings
							</h3>
							<p className="card-description">Choose and configure your trading strategy</p>
						</div>

						<div className="card-content space-y-4">
							<div className="form-group">
								<label className="form-label">Trading Strategy</label>
								<select {...register('strategy')} className="select">
									{strategyOptions.map(option => (
										<option key={option.value} value={option.value}>
											{option.label}
										</option>
									))}
								</select>
							</div>

							<div className="form-group">
								<label className="form-label">Time Period</label>
								<select {...register('timePeriod')} className="select">
									{timePeriodOptions.map(option => (
										<option key={option.value} value={option.value}>
											{option.label}
										</option>
									))}
								</select>
							</div>

							<div className="form-group">
								<label className="form-label">Confidence Threshold (%)</label>
								<input
									type="number"
									min="50"
									max="100"
									step="1"
									{...register('confidenceThreshold', { valueAsNumber: true })}
									className="input"
								/>
								<p className="text-xs text-dark-400 mt-1">Minimum confidence level required to execute trades</p>
							</div>
						</div>
					</div>

					{/* Asset Filter */}
					<div className="card">
						<div className="card-header">
							<h3 className="card-title flex items-center gap-2">
								<Filter className="h-5 w-5" />
								Asset Filter
							</h3>
							<p className="card-description">Select which assets to trade</p>
						</div>

						<div className="card-content space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div className="form-group">
									<label className="form-label">Asset Type</label>
									<select {...register('assetFilter.type')} className="select">
										<option value="OTC">OTC</option>
										<option value="ALL">All</option>
									</select>
								</div>

								<div className="form-group">
									<label className="form-label">Category</label>
									<select {...register('assetFilter.category')} className="select">
										{categoryOptions.map(option => (
											<option key={option.value} value={option.value}>
												{option.label}
											</option>
										))}
									</select>
								</div>
							</div>

							<div className="form-group">
								<label className="form-label">Available Assets</label>
								<div className="max-h-32 overflow-y-auto border border-dark-600 rounded-md p-2 space-y-1">
									{availableAssets.length > 0 ? (
										availableAssets.slice(0, 10).map(asset => (
											<label key={asset.id} className="flex items-center gap-2 text-sm">
												<input
													type="checkbox"
													value={asset.name}
													{...register('assetFilter.selectedAssets')}
													className="rounded border-dark-600"
												/>
												<span>{asset.name}</span>
												<span className="text-xs text-dark-400">({asset.payoutPercentage}%)</span>
											</label>
										))
									) : (
										<p className="text-sm text-dark-400">No assets available. Please authenticate first.</p>
									)}
								</div>
							</div>
						</div>
					</div>

					{/* Advanced Settings */}
					<div className="card">
						<div className="card-header">
							<h3 className="card-title flex items-center gap-2">
								<Zap className="h-5 w-5" />
								Advanced Settings
							</h3>
							<p className="card-description">Additional configuration options</p>
						</div>

						<div className="card-content space-y-4">
							<div className="space-y-3">
								<label className="flex items-center gap-3">
									<input type="checkbox" {...register('dryRun')} className="rounded border-dark-600" />
									<div>
										<span className="font-medium">Dry Run Mode</span>
										<p className="text-xs text-dark-400">Simulate trades without real money</p>
									</div>
								</label>

								<label className="flex items-center gap-3">
									<input type="checkbox" {...register('debugLogs')} className="rounded border-dark-600" />
									<div>
										<span className="font-medium">Debug Logs</span>
										<p className="text-xs text-dark-400">Enable detailed logging for troubleshooting</p>
									</div>
								</label>

								<label className="flex items-center gap-3">
									<input type="checkbox" {...register('headlessBrowser')} className="rounded border-dark-600" />
									<div>
										<span className="font-medium">Headless Browser</span>
										<p className="text-xs text-dark-400">Hide browser window (recommended for single window mode)</p>
									</div>
								</label>
							</div>

							<div className="pt-4 border-t border-dark-600">
								<button
									type="button"
									onClick={handleSelectScreenshotArea}
									className="btn-secondary w-full flex items-center justify-center gap-2"
								>
									<Camera className="h-4 w-4" />
									Select Chart Area
								</button>
								<p className="text-xs text-dark-400 mt-2 text-center">
									Click to select the chart area for price analysis
								</p>
							</div>
						</div>
					</div>
				</div>
			</form>
		</div>
	)
}
