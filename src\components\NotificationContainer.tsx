import React from 'react'
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react'
import { useBotStore } from '../store/useBotStore'
import { Notification } from '../types'
import { clsx } from 'clsx'

export const NotificationContainer: React.FC = () => {
  const { notifications, removeNotification } = useBotStore()

  if (notifications.length === 0) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  )
}

interface NotificationItemProps {
  notification: Notification
  onClose: () => void
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onClose
}) => {
  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return CheckCircle
      case 'error':
        return AlertCircle
      case 'warning':
        return AlertTriangle
      default:
        return Info
    }
  }

  const getColorClasses = () => {
    switch (notification.type) {
      case 'success':
        return 'bg-success-900 border-success-600 text-success-100'
      case 'error':
        return 'bg-danger-900 border-danger-600 text-danger-100'
      case 'warning':
        return 'bg-yellow-900 border-yellow-600 text-yellow-100'
      default:
        return 'bg-primary-900 border-primary-600 text-primary-100'
    }
  }

  const Icon = getIcon()

  return (
    <div className={clsx(
      'p-4 rounded-lg border shadow-lg backdrop-blur-sm',
      'transform transition-all duration-300 ease-in-out',
      'animate-in slide-in-from-right-full',
      getColorClasses()
    )}>
      <div className="flex items-start gap-3">
        <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
        
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm">{notification.title}</h4>
          <p className="text-sm opacity-90 mt-1">{notification.message}</p>
          
          <div className="text-xs opacity-75 mt-2">
            {new Date(notification.timestamp).toLocaleTimeString()}
          </div>
        </div>
        
        <button
          onClick={onClose}
          className="flex-shrink-0 p-1 rounded-md hover:bg-black/20 transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  )
}
