import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { 
  Shield, 
  User, 
  Lock, 
  LogIn, 
  AlertCircle,
  CheckCircle,
  Globe,
  Settings
} from 'lucide-react'
import { useBotStore } from '../store/useBotStore'
import { AuthCredentials } from '../types'
import { clsx } from 'clsx'

interface AuthForm {
  email: string
  password: string
  sessionCookies: string
}

export const Authentication: React.FC = () => {
  const { 
    isAuthenticated, 
    setAuthenticated, 
    addNotification,
    isConnected
  } = useBotStore()
  
  const [isLoading, setIsLoading] = useState(false)
  const [authMethod, setAuthMethod] = useState<'credentials' | 'cookies'>('credentials')
  
  const { register, handleSubmit, formState: { errors }, reset } = useForm<AuthForm>()

  const onSubmit = async (data: AuthForm) => {
    if (!window.electronAPI) {
      addNotification({
        type: 'error',
        title: 'Not Connected',
        message: 'Electron API not available. Please run in Electron mode.',
        autoClose: false
      })
      return
    }

    setIsLoading(true)
    
    try {
      // First initialize the bot
      const initResult = await window.electronAPI.bot.initialize({})
      if (!initResult.success) {
        throw new Error(initResult.message)
      }

      // Then authenticate
      const credentials: AuthCredentials = authMethod === 'credentials' 
        ? { email: data.email, password: data.password }
        : { sessionCookies: data.sessionCookies }

      const result = await window.electronAPI.browser.authenticate(credentials)
      
      if (result.success) {
        setAuthenticated(true)
        addNotification({
          type: 'success',
          title: 'Authentication Successful',
          message: 'Successfully connected to Pocket Option',
          autoClose: true
        })
        reset()
      } else {
        addNotification({
          type: 'error',
          title: 'Authentication Failed',
          message: result.message || 'Failed to authenticate',
          autoClose: false
        })
      }
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Authentication Error',
        message: error.message || 'An unexpected error occurred',
        autoClose: false
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    setAuthenticated(false)
    addNotification({
      type: 'info',
      title: 'Logged Out',
      message: 'You have been logged out',
      autoClose: true
    })
  }

  const openPocketOption = () => {
    if (window.electronAPI) {
      // In Electron, this would open the browser
      addNotification({
        type: 'info',
        title: 'Opening Pocket Option',
        message: 'Browser window will open shortly',
        autoClose: true
      })
    } else {
      // In web mode, open in new tab
      window.open('https://pocketoption.com/en/cabinet/demo-quick-high-low/', '_blank')
    }
  }

  if (isAuthenticated) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="card max-w-md w-full">
          <div className="card-content text-center space-y-6">
            <div className="flex justify-center">
              <div className="p-4 bg-success-900 rounded-full">
                <CheckCircle className="h-8 w-8 text-success-100" />
              </div>
            </div>
            
            <div>
              <h2 className="text-xl font-bold mb-2">Successfully Authenticated</h2>
              <p className="text-dark-400">
                You are now connected to Pocket Option and ready to start trading.
              </p>
            </div>
            
            <div className="space-y-3">
              <button
                onClick={openPocketOption}
                className="btn-primary w-full flex items-center justify-center gap-2"
              >
                <Globe className="h-4 w-4" />
                Open Pocket Option
              </button>
              
              <button
                onClick={handleLogout}
                className="btn-secondary w-full"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold flex items-center justify-center gap-2 mb-2">
            <Shield className="h-6 w-6" />
            Authentication
          </h1>
          <p className="text-dark-400">
            Connect to your Pocket Option account to start trading
          </p>
        </div>

        {/* Connection Status */}
        {!isConnected && (
          <div className="card border-yellow-600 bg-yellow-900/20">
            <div className="card-content">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-yellow-400" />
                <div>
                  <h3 className="font-medium text-yellow-100">Not Connected</h3>
                  <p className="text-sm text-yellow-200">
                    Please run the application in Electron mode for full functionality.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Authentication Methods */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Authentication Method</h3>
            <p className="card-description">
              Choose how you want to authenticate with Pocket Option
            </p>
          </div>
          
          <div className="card-content">
            <div className="flex gap-2 mb-6">
              <button
                onClick={() => setAuthMethod('credentials')}
                className={clsx(
                  'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors',
                  authMethod === 'credentials'
                    ? 'bg-primary-600 text-white'
                    : 'bg-dark-700 text-dark-300 hover:bg-dark-600'
                )}
              >
                Email & Password
              </button>
              
              <button
                onClick={() => setAuthMethod('cookies')}
                className={clsx(
                  'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors',
                  authMethod === 'cookies'
                    ? 'bg-primary-600 text-white'
                    : 'bg-dark-700 text-dark-300 hover:bg-dark-600'
                )}
              >
                Session Cookies
              </button>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {authMethod === 'credentials' ? (
                <>
                  <div className="form-group">
                    <label className="form-label">Email Address</label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-dark-400" />
                      <input
                        type="email"
                        {...register('email', { 
                          required: 'Email is required',
                          pattern: {
                            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                            message: 'Invalid email address'
                          }
                        })}
                        className="input pl-10"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    {errors.email && (
                      <p className="form-error">{errors.email.message}</p>
                    )}
                  </div>

                  <div className="form-group">
                    <label className="form-label">Password</label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-dark-400" />
                      <input
                        type="password"
                        {...register('password', { 
                          required: 'Password is required',
                          minLength: {
                            value: 6,
                            message: 'Password must be at least 6 characters'
                          }
                        })}
                        className="input pl-10"
                        placeholder="Your password"
                      />
                    </div>
                    {errors.password && (
                      <p className="form-error">{errors.password.message}</p>
                    )}
                  </div>
                </>
              ) : (
                <div className="form-group">
                  <label className="form-label">Session Cookies (JSON)</label>
                  <textarea
                    {...register('sessionCookies', { 
                      required: 'Session cookies are required'
                    })}
                    className="input min-h-32 font-mono text-xs"
                    placeholder='[{"name": "session", "value": "...", "domain": ".pocketoption.com"}]'
                  />
                  {errors.sessionCookies && (
                    <p className="form-error">{errors.sessionCookies.message}</p>
                  )}
                  <p className="text-xs text-dark-400 mt-1">
                    Paste your exported session cookies in JSON format
                  </p>
                </div>
              )}

              <button
                type="submit"
                disabled={isLoading || !isConnected}
                className="btn-primary w-full flex items-center justify-center gap-2 disabled:opacity-50"
              >
                {isLoading ? (
                  <>
                    <div className="loading-spinner" />
                    Authenticating...
                  </>
                ) : (
                  <>
                    <LogIn className="h-4 w-4" />
                    Authenticate
                  </>
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Instructions */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Setup Instructions
            </h3>
          </div>
          
          <div className="card-content">
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">Method 1: Email & Password</h4>
                <ol className="list-decimal list-inside space-y-1 text-dark-400">
                  <li>Enter your Pocket Option email and password</li>
                  <li>Click "Authenticate" to log in automatically</li>
                  <li>The bot will handle the login process</li>
                </ol>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Method 2: Session Cookies</h4>
                <ol className="list-decimal list-inside space-y-1 text-dark-400">
                  <li>Log in to Pocket Option manually in your browser</li>
                  <li>Export your session cookies using browser dev tools</li>
                  <li>Paste the JSON cookies in the text area above</li>
                  <li>Click "Authenticate" to use the saved session</li>
                </ol>
              </div>
              
              <div className="p-3 bg-yellow-900/20 border border-yellow-600 rounded-md">
                <p className="text-yellow-200 text-xs">
                  <strong>Security Note:</strong> Your credentials are only used to authenticate 
                  with Pocket Option and are not stored or transmitted elsewhere. Session cookies 
                  are saved locally for convenience.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
