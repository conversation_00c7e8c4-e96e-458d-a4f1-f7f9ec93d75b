import React from 'react'
import { LucideIcon } from 'lucide-react'
import { clsx } from 'clsx'

interface MetricCardProps {
  title: string
  value: string
  icon: LucideIcon
  trend?: 'up' | 'down' | 'neutral'
  trendValue?: string
  className?: string
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon: Icon,
  trend = 'neutral',
  trendValue,
  className
}) => {
  return (
    <div className={clsx('metric-card', className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={clsx(
            'p-2 rounded-lg',
            trend === 'up' && 'bg-success-900 text-success-100',
            trend === 'down' && 'bg-danger-900 text-danger-100',
            trend === 'neutral' && 'bg-dark-700 text-dark-300'
          )}>
            <Icon className="h-4 w-4" />
          </div>
          
          <div>
            <div className="metric-label">{title}</div>
            <div className="metric-value">{value}</div>
          </div>
        </div>
      </div>
      
      {trendValue && (
        <div className="mt-2">
          <div className={clsx(
            'metric-change text-xs',
            trend === 'up' && 'metric-positive',
            trend === 'down' && 'metric-negative',
            trend === 'neutral' && 'text-dark-400'
          )}>
            {trendValue}
          </div>
        </div>
      )}
    </div>
  )
}
