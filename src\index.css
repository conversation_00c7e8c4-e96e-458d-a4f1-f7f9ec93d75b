@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	* {
		@apply border-dark-600;
	}

	body {
		@apply bg-dark-950 text-dark-50 font-sans;
	}
}

@layer components {
	.btn {
		@apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
	}

	.btn-primary {
		@apply btn bg-primary-600 text-white hover:bg-primary-700 h-10 py-2 px-4;
	}

	.btn-secondary {
		@apply btn bg-dark-700 text-dark-100 hover:bg-dark-600 h-10 py-2 px-4;
	}

	.btn-success {
		@apply btn bg-success-600 text-white hover:bg-success-700 h-10 py-2 px-4;
	}

	.btn-danger {
		@apply btn bg-danger-600 text-white hover:bg-danger-700 h-10 py-2 px-4;
	}

	.btn-sm {
		@apply h-8 px-3 text-xs;
	}

	.btn-lg {
		@apply h-12 px-8 text-base;
	}

	.input {
		@apply flex h-10 w-full rounded-md border border-dark-600 bg-dark-800 px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-dark-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
	}

	.select {
		@apply input cursor-pointer;
	}

	.card {
		@apply rounded-lg border border-dark-700 bg-dark-800 p-6 shadow-lg;
	}

	.card-header {
		@apply flex flex-col space-y-1.5 pb-4;
	}

	.card-title {
		@apply text-lg font-semibold leading-none tracking-tight;
	}

	.card-description {
		@apply text-sm text-dark-400;
	}

	.card-content {
		@apply pt-0;
	}

	.badge {
		@apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
	}

	.badge-success {
		@apply badge bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-100;
	}

	.badge-danger {
		@apply badge bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-100;
	}

	.badge-warning {
		@apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100;
	}

	.badge-info {
		@apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-100;
	}

	.status-indicator {
		@apply inline-flex items-center gap-2 text-sm font-medium;
	}

	.status-dot {
		@apply h-2 w-2 rounded-full;
	}

	.status-running .status-dot {
		@apply bg-success-500 animate-pulse;
	}

	.status-stopped .status-dot {
		@apply bg-dark-500;
	}

	.status-error .status-dot {
		@apply bg-danger-500 animate-pulse;
	}

	.status-starting .status-dot {
		@apply bg-yellow-500 animate-pulse;
	}

	.grid-layout {
		@apply grid grid-cols-12 gap-4 h-full;
	}

	.sidebar {
		@apply col-span-3 space-y-4 overflow-y-auto;
	}

	.main-content {
		@apply col-span-6 space-y-4 overflow-y-auto;
	}

	.right-panel {
		@apply col-span-3 space-y-4 overflow-y-auto;
	}

	.form-group {
		@apply space-y-2;
	}

	.form-label {
		@apply text-sm font-medium text-dark-200;
	}

	.form-error {
		@apply text-sm text-danger-400;
	}

	.trade-item {
		@apply flex items-center justify-between p-3 rounded-md border border-dark-600 bg-dark-700/50;
	}

	.trade-win {
		@apply border-success-600/50 bg-success-900/20;
	}

	.trade-loss {
		@apply border-danger-600/50 bg-danger-900/20;
	}

	.trade-pending {
		@apply border-yellow-600/50 bg-yellow-900/20;
	}

	.metric-card {
		@apply card p-4;
	}

	.metric-value {
		@apply text-2xl font-bold;
	}

	.metric-label {
		@apply text-sm text-dark-400;
	}

	.metric-change {
		@apply text-sm font-medium;
	}

	.metric-positive {
		@apply text-success-400;
	}

	.metric-negative {
		@apply text-danger-400;
	}

	.loading-spinner {
		@apply animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500;
	}

	.confidence-bar {
		@apply w-full bg-dark-700 rounded-full h-2;
	}

	.confidence-fill {
		@apply h-2 rounded-full transition-all duration-300;
	}

	.confidence-low {
		@apply bg-danger-500;
	}

	.confidence-medium {
		@apply bg-yellow-500;
	}

	.confidence-high {
		@apply bg-success-500;
	}
}
