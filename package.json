{"name": "pocket-option-trading-bot", "version": "1.0.0", "description": "Advanced trading bot for Pocket Option with Electron GUI", "main": "dist-electron/main.js", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build && electron-builder", "build:web": "tsc && vite build", "build:electron": "electron-builder", "preview": "vite preview", "electron": "wait-on tcp:5173 && cross-env NODE_ENV=development VITE_DEV_SERVER_URL=http://localhost:5173 electron ./dist-electron/main.js", "electron:dev": "concurrently \"pnpm dev\" \"pnpm electron\"", "electron:pack": "electron-builder --dir", "electron:dist": "electron-builder", "postinstall": "electron-builder install-app-deps", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "keywords": ["trading-bot", "pocket-option", "electron", "react", "puppeteer", "automation"], "author": "Trading Bot Developer", "license": "MIT", "dependencies": {"axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^3.0.6", "electron": "^28.1.0", "lodash": "^4.17.21", "lucide-react": "^0.303.0", "playwright": "^1.40.1", "puppeteer": "^21.6.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "tesseract.js": "^5.0.4", "ws": "^8.16.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/node": "^20.10.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-electron": "^0.28.1", "vite-plugin-electron-renderer": "^0.14.5", "wait-on": "^7.2.0"}, "build": {"appId": "com.tradingbot.pocketoption", "productName": "Pocket Option Trading Bot", "directories": {"output": "release"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.finance"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild", "puppeteer", "tesseract.js"]}}