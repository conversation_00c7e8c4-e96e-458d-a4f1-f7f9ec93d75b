import Tesseract from 'tesseract.js'
import { ChartAnalysis, OCRResult, ChartCoordinates, MarketData } from '../../src/types'

export class OCRService {
  private worker: Tesseract.Worker | null = null
  private isInitialized: boolean = false

  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      this.worker = await Tesseract.createWorker('eng', 1, {
        logger: m => {
          if (m.status === 'recognizing text') {
            console.log(`OCR Progress: ${(m.progress * 100).toFixed(1)}%`)
          }
        }
      })

      await this.worker.setParameters({
        tessedit_char_whitelist: '0123456789.,+-$€£¥%',
        tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,
        preserve_interword_spaces: '0'
      })

      this.isInitialized = true
      console.log('OCR Service initialized successfully')
    } catch (error) {
      console.error('Failed to initialize OCR service:', error)
      throw error
    }
  }

  async analyzeChart(screenshot: <PERSON>uffer, coordinates?: ChartCoordinates): Promise<ChartAnalysis> {
    if (!this.isInitialized || !this.worker) {
      throw new Error('OCR Service not initialized')
    }

    try {
      // Extract price information from screenshot
      const priceData = await this.extractPriceData(screenshot)
      
      // Calculate technical indicators (simplified)
      const indicators = this.calculateIndicators(priceData)
      
      // Determine trend
      const trend = this.determineTrend(priceData, indicators)
      
      // Calculate confidence based on data quality
      const confidence = this.calculateConfidence(priceData, indicators)

      return {
        currentPrice: priceData.currentPrice,
        priceChange: priceData.priceChange,
        priceChangePercent: priceData.priceChangePercent,
        trend,
        support: indicators.support,
        resistance: indicators.resistance,
        rsi: indicators.rsi,
        ema: {
          short: indicators.emaShort,
          long: indicators.emaLong
        },
        confidence,
        timestamp: Date.now()
      }
    } catch (error) {
      console.error('Chart analysis failed:', error)
      throw error
    }
  }

  private async extractPriceData(screenshot: Buffer): Promise<{
    currentPrice: number
    priceChange: number
    priceChangePercent: number
    rawText: string
  }> {
    if (!this.worker) {
      throw new Error('OCR worker not available')
    }

    const { data: { text } } = await this.worker.recognize(screenshot)
    
    // Parse price information from OCR text
    const priceRegex = /(\d+\.?\d*)/g
    const matches = text.match(priceRegex)
    
    if (!matches || matches.length === 0) {
      throw new Error('No price data found in screenshot')
    }

    // Extract current price (usually the largest or most prominent number)
    const prices = matches.map(match => parseFloat(match)).filter(price => !isNaN(price))
    const currentPrice = prices.length > 0 ? prices[0] : 0

    // Try to extract price change information
    const changeRegex = /([+-]?\d+\.?\d*)[%]?/g
    const changeMatches = text.match(changeRegex)
    
    let priceChange = 0
    let priceChangePercent = 0
    
    if (changeMatches && changeMatches.length > 1) {
      priceChange = parseFloat(changeMatches[1]) || 0
      
      // Look for percentage change
      const percentMatch = text.match(/([+-]?\d+\.?\d*)%/)
      if (percentMatch) {
        priceChangePercent = parseFloat(percentMatch[1]) || 0
      }
    }

    return {
      currentPrice,
      priceChange,
      priceChangePercent,
      rawText: text
    }
  }

  private calculateIndicators(priceData: any): {
    support: number
    resistance: number
    rsi: number
    emaShort: number
    emaLong: number
    volatility: number
  } {
    // Simplified indicator calculations
    // In a real implementation, you would need historical price data
    const currentPrice = priceData.currentPrice
    
    // Estimate support and resistance based on current price
    const support = currentPrice * 0.98 // 2% below current price
    const resistance = currentPrice * 1.02 // 2% above current price
    
    // Simplified RSI calculation (would need historical data for accuracy)
    // For now, estimate based on price change
    let rsi = 50 // Neutral
    if (priceData.priceChangePercent > 2) {
      rsi = 70 // Overbought
    } else if (priceData.priceChangePercent < -2) {
      rsi = 30 // Oversold
    } else if (priceData.priceChangePercent > 0) {
      rsi = 55 + (priceData.priceChangePercent * 5)
    } else {
      rsi = 45 + (priceData.priceChangePercent * 5)
    }
    
    // Simplified EMA calculations
    const emaShort = currentPrice * (1 + (priceData.priceChangePercent / 100) * 0.5)
    const emaLong = currentPrice * (1 + (priceData.priceChangePercent / 100) * 0.2)
    
    // Estimate volatility based on price change
    const volatility = Math.abs(priceData.priceChangePercent) / 100

    return {
      support: Math.max(support, 0),
      resistance,
      rsi: Math.max(0, Math.min(100, rsi)),
      emaShort,
      emaLong,
      volatility
    }
  }

  private determineTrend(priceData: any, indicators: any): 'up' | 'down' | 'sideways' {
    const priceChange = priceData.priceChangePercent
    const emaSignal = indicators.emaShort > indicators.emaLong
    
    if (priceChange > 0.5 && emaSignal) {
      return 'up'
    } else if (priceChange < -0.5 && !emaSignal) {
      return 'down'
    } else {
      return 'sideways'
    }
  }

  private calculateConfidence(priceData: any, indicators: any): number {
    let confidence = 50 // Base confidence
    
    // Increase confidence if we have clear price data
    if (priceData.currentPrice > 0) {
      confidence += 20
    }
    
    // Increase confidence if price change is significant
    if (Math.abs(priceData.priceChangePercent) > 1) {
      confidence += 15
    }
    
    // Increase confidence if RSI is in extreme zones
    if (indicators.rsi <= 30 || indicators.rsi >= 70) {
      confidence += 10
    }
    
    // Decrease confidence if volatility is too high
    if (indicators.volatility > 0.05) {
      confidence -= 15
    }
    
    return Math.max(0, Math.min(100, confidence))
  }

  async extractText(screenshot: Buffer, coordinates?: ChartCoordinates): Promise<OCRResult[]> {
    if (!this.isInitialized || !this.worker) {
      throw new Error('OCR Service not initialized')
    }

    try {
      const { data } = await this.worker.recognize(screenshot)
      
      const results: OCRResult[] = []
      
      if (data.words) {
        for (const word of data.words) {
          if (word.confidence > 60) { // Only include high-confidence results
            results.push({
              text: word.text,
              confidence: word.confidence,
              bbox: {
                x0: word.bbox.x0,
                y0: word.bbox.y0,
                x1: word.bbox.x1,
                y1: word.bbox.y1
              }
            })
          }
        }
      }
      
      return results
    } catch (error) {
      console.error('Text extraction failed:', error)
      throw error
    }
  }

  // Convert chart analysis to market data format
  chartAnalysisToMarketData(analysis: ChartAnalysis, asset: string): MarketData {
    return {
      asset,
      price: analysis.currentPrice,
      timestamp: analysis.timestamp,
      rsi: analysis.rsi,
      emaShort: analysis.ema.short,
      emaLong: analysis.ema.long,
      volatility: Math.abs(analysis.priceChangePercent) / 100,
      trend: analysis.trend,
      confidence: analysis.confidence
    }
  }

  async cleanup(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate()
      this.worker = null
      this.isInitialized = false
    }
  }
}
