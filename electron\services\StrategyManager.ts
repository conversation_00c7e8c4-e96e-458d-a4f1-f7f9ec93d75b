import { BaseStrategy, StrategyType, StrategyConfig, MarketData, TradeDecision } from '../../src/types'
import { BaseStrategyImpl } from './BaseStrategy'
import { MartingaleStrategy } from './strategies/MartingaleStrategy'
import { DAlembertStrategy } from './strategies/DAlembertStrategy'
import { FixedRSIStrategy } from './strategies/FixedRSIStrategy'

export class StrategyManager {
	private static strategies: Map<StrategyType, typeof BaseStrategy> = new Map([
		['martingale', MartingaleStrategy as any],
		['dalembert', DAlembertStrategy as any],
		['fixed-rsi', FixedRSIStrategy as any]
	])

	private activeStrategy: BaseStrategy | null = null

	static getAvailableStrategies(): Array<{ name: StrategyType; description: string; defaultConfig: StrategyConfig }> {
		return [
			{
				name: 'martingale',
				description: 'Doubles bet after each loss, resets on win. High risk, high reward.',
				defaultConfig: {
					baseAmount: 1,
					multiplier: 2,
					maxSteps: 5,
					resetOnWin: true
				}
			},
			{
				name: 'da<PERSON><PERSON>',
				description: 'Increases bet by fixed amount after loss, decreases after win. Moderate risk.',
				defaultConfig: {
					baseAmount: 1,
					increment: 1,
					maxSteps: 10
				}
			},
			{
				name: 'fixed-rsi',
				description: 'Fixed stake with RSI and EMA indicators. Conservative approach.',
				defaultConfig: {
					amount: 1,
					rsiPeriod: 14,
					oversoldThreshold: 30,
					overboughtThreshold: 70,
					emaShort: 12,
					emaLong: 26
				}
			}
		]
	}

	setStrategy(strategyType: StrategyType, config: StrategyConfig): void {
		const StrategyClass = StrategyManager.strategies.get(strategyType)

		if (!StrategyClass) {
			throw new Error(`Strategy ${strategyType} not found`)
		}

		this.activeStrategy = new (StrategyClass as any)(config)
	}

	async executeStrategy(marketData: MarketData): Promise<TradeDecision | null> {
		if (!this.activeStrategy) {
			throw new Error('No active strategy set')
		}

		// Calculate confidence first
		const confidence = this.activeStrategy.calculateConfidence(marketData)

		// Only execute if confidence is high enough
		if (confidence < 87) {
			// Configurable threshold
			return {
				action: 'hold',
				amount: 0,
				confidence,
				reasoning: `Confidence too low: ${confidence.toFixed(2)}%`
			}
		}

		return await this.activeStrategy.execute(marketData)
	}

	getActiveStrategy(): BaseStrategy | null {
		return this.activeStrategy
	}

	resetStrategy(): void {
		if (this.activeStrategy) {
			this.activeStrategy.reset()
		}
	}
}
