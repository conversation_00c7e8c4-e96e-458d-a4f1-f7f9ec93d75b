import React from 'react'
import { TrendingUp, TrendingDown, Clock, DollarSign } from 'lucide-react'
import { Trade } from '../types'
import { clsx } from 'clsx'

interface TradeItemProps {
  trade: Trade
  showDetails?: boolean
}

export const TradeItem: React.FC<TradeItemProps> = ({ 
  trade, 
  showDetails = false 
}) => {
  const formatTime = (timestamp: number) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(new Date(timestamp))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const getResultColor = () => {
    switch (trade.result) {
      case 'win':
        return 'text-success-400'
      case 'loss':
        return 'text-danger-400'
      default:
        return 'text-yellow-400'
    }
  }

  const getResultBadge = () => {
    switch (trade.result) {
      case 'win':
        return 'badge-success'
      case 'loss':
        return 'badge-danger'
      default:
        return 'badge-warning'
    }
  }

  return (
    <div className={clsx(
      'trade-item',
      trade.result === 'win' && 'trade-win',
      trade.result === 'loss' && 'trade-loss',
      trade.result === 'pending' && 'trade-pending'
    )}>
      <div className="flex items-center gap-3 flex-1">
        {/* Direction Icon */}
        <div className={clsx(
          'p-1.5 rounded-full',
          trade.direction === 'high' ? 'bg-success-900 text-success-100' : 'bg-danger-900 text-danger-100'
        )}>
          {trade.direction === 'high' ? (
            <TrendingUp className="h-3 w-3" />
          ) : (
            <TrendingDown className="h-3 w-3" />
          )}
        </div>

        {/* Trade Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{trade.asset}</span>
            <span className={clsx('badge text-xs', getResultBadge())}>
              {trade.result || 'pending'}
            </span>
          </div>
          
          <div className="flex items-center gap-4 text-xs text-dark-400 mt-1">
            <span className="flex items-center gap-1">
              <DollarSign className="h-3 w-3" />
              {formatCurrency(trade.amount)}
            </span>
            
            <span className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {formatTime(trade.openTime)}
            </span>
            
            <span className="capitalize">
              {trade.strategy}
            </span>
          </div>
        </div>

        {/* Profit/Loss */}
        <div className="text-right">
          {trade.profit !== undefined ? (
            <div className={clsx('font-medium', getResultColor())}>
              {trade.profit >= 0 ? '+' : ''}{formatCurrency(trade.profit)}
            </div>
          ) : (
            <div className="text-dark-400 text-sm">
              Pending
            </div>
          )}
          
          <div className="text-xs text-dark-400">
            {(trade.confidence || 0).toFixed(1)}% confidence
          </div>
        </div>
      </div>

      {/* Detailed Info (if showDetails is true) */}
      {showDetails && (
        <div className="mt-3 pt-3 border-t border-dark-600 grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-dark-400">Open Price:</span>
            <span className="ml-2 font-medium">{trade.openPrice.toFixed(5)}</span>
          </div>
          
          {trade.closePrice && (
            <div>
              <span className="text-dark-400">Close Price:</span>
              <span className="ml-2 font-medium">{trade.closePrice.toFixed(5)}</span>
            </div>
          )}
          
          <div>
            <span className="text-dark-400">Duration:</span>
            <span className="ml-2 font-medium">{trade.duration / 1000}s</span>
          </div>
          
          {trade.closeTime && (
            <div>
              <span className="text-dark-400">Closed:</span>
              <span className="ml-2 font-medium">{formatTime(trade.closeTime)}</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
