// Trading Bot Types
export interface BotConfig {
  accountType: 'demo' | 'live'
  strategyMode: 'oscillate' | 'slide'
  tradeCapital: number
  targetProfit: number
  tradeAmount: number
  strategy: StrategyType
  assetFilter: AssetFilter
  timePeriod: TimePeriod
  chartCoordinates?: ChartCoordinates
  confidenceThreshold: number
  dryRun: boolean
  debugLogs: boolean
}

export interface AssetFilter {
  type: 'OTC' | 'ALL'
  category: 'currency' | 'crypto' | 'commodities' | 'stocks' | 'indices'
  selectedAssets: string[]
}

export interface ChartCoordinates {
  x: number
  y: number
  width: number
  height: number
}

export type TimePeriod = 'S5' | 'S15' | 'S30' | 'M1' | 'M3' | 'M5' | 'M15' | 'M30' | 'H1'

export type StrategyType = 'martingale' | 'dalembert' | 'fixed-rsi'

// Strategy Interfaces
export interface BaseStrategy {
  name: string
  description: string
  config: StrategyConfig
  execute(marketData: MarketData): Promise<TradeDecision>
  calculateConfidence(marketData: MarketData): number
  reset(): void
}

export interface StrategyConfig {
  [key: string]: any
}

export interface MartingaleConfig extends StrategyConfig {
  baseAmount: number
  multiplier: number
  maxSteps: number
  resetOnWin: boolean
}

export interface DAlembertConfig extends StrategyConfig {
  baseAmount: number
  increment: number
  maxSteps: number
}

export interface FixedRSIConfig extends StrategyConfig {
  amount: number
  rsiPeriod: number
  oversoldThreshold: number
  overboughtThreshold: number
  emaShort: number
  emaLong: number
}

// Market Data Types
export interface MarketData {
  asset: string
  price: number
  timestamp: number
  rsi: number
  emaShort: number
  emaLong: number
  volume?: number
  volatility: number
  trend: 'up' | 'down' | 'sideways'
  confidence: number
}

export interface PriceData {
  open: number
  high: number
  low: number
  close: number
  timestamp: number
}

// Trading Types
export interface TradeDecision {
  action: 'buy' | 'sell' | 'hold'
  amount: number
  confidence: number
  reasoning: string
  metadata?: Record<string, any>
}

export interface Trade {
  id: string
  asset: string
  direction: 'high' | 'low'
  amount: number
  openPrice: number
  closePrice?: number
  openTime: number
  closeTime?: number
  duration: number
  result?: 'win' | 'loss' | 'pending'
  profit?: number
  strategy: string
  confidence: number
}

export interface TradingSession {
  startTime: number
  endTime?: number
  totalTrades: number
  winningTrades: number
  losingTrades: number
  totalProfit: number
  winRate: number
  maxDrawdown: number
  currentStreak: number
  bestStreak: number
  worstStreak: number
}

// Browser Automation Types
export interface BrowserConfig {
  headless: boolean
  devtools: boolean
  userDataDir: string
  viewport: {
    width: number
    height: number
  }
}

export interface AuthCredentials {
  email?: string
  password?: string
  sessionCookies?: string
}

export interface Asset {
  id: string
  name: string
  symbol: string
  category: string
  type: 'OTC' | 'REGULAR'
  isActive: boolean
  minTradeAmount: number
  maxTradeAmount: number
  payoutPercentage: number
}

// UI State Types
export interface AppState {
  isConnected: boolean
  isAuthenticated: boolean
  botStatus: 'stopped' | 'starting' | 'running' | 'stopping' | 'error'
  currentSession?: TradingSession
  recentTrades: Trade[]
  availableAssets: Asset[]
  selectedAsset?: Asset
  marketData?: MarketData
  error?: string
  notifications: Notification[]
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: number
  autoClose?: boolean
}

// OCR and Analysis Types
export interface OCRResult {
  text: string
  confidence: number
  bbox: {
    x0: number
    y0: number
    x1: number
    y1: number
  }
}

export interface ChartAnalysis {
  currentPrice: number
  priceChange: number
  priceChangePercent: number
  trend: 'up' | 'down' | 'sideways'
  support: number
  resistance: number
  rsi: number
  ema: {
    short: number
    long: number
  }
  confidence: number
  timestamp: number
}

// Error Types
export interface BotError {
  code: string
  message: string
  details?: any
  timestamp: number
  recoverable: boolean
}

// Event Types
export type BotEvent = 
  | { type: 'status_changed'; status: string; data?: any }
  | { type: 'trade_opened'; trade: Trade }
  | { type: 'trade_closed'; trade: Trade }
  | { type: 'market_data_updated'; data: MarketData }
  | { type: 'error_occurred'; error: BotError }
  | { type: 'notification'; notification: Notification }
