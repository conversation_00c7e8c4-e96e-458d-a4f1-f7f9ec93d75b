import React from 'react'
import { Wifi, WifiOff, Shield, Alert<PERSON>riangle, Clock } from 'lucide-react'
import { useBotStore } from '../store/useBotStore'
import { clsx } from 'clsx'

export const StatusBar: React.FC = () => {
  const { 
    isConnected, 
    isAuthenticated, 
    botStatus, 
    currentSession,
    selectedAsset,
    marketData
  } = useBotStore()

  const formatTime = (timestamp: number) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(new Date(timestamp))
  }

  const getSessionDuration = () => {
    if (!currentSession) return null
    
    const duration = Date.now() - currentSession.startTime
    const minutes = Math.floor(duration / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  return (
    <div className="bg-dark-800 border-b border-dark-700 px-4 py-2">
      <div className="flex items-center justify-between">
        {/* Left side - Connection and Auth status */}
        <div className="flex items-center gap-4">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            {isConnected ? (
              <>
                <Wifi className="h-4 w-4 text-success-400" />
                <span className="text-sm text-success-400">Connected</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-danger-400" />
                <span className="text-sm text-danger-400">Disconnected</span>
              </>
            )}
          </div>

          {/* Authentication Status */}
          {isConnected && (
            <div className="flex items-center gap-2">
              {isAuthenticated ? (
                <>
                  <Shield className="h-4 w-4 text-success-400" />
                  <span className="text-sm text-success-400">Authenticated</span>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  <span className="text-sm text-yellow-400">Not Authenticated</span>
                </>
              )}
            </div>
          )}

          {/* Bot Status */}
          <div className="flex items-center gap-2">
            <div className={clsx(
              'h-2 w-2 rounded-full',
              botStatus === 'running' && 'bg-success-500 animate-pulse',
              botStatus === 'stopped' && 'bg-dark-500',
              botStatus === 'starting' && 'bg-yellow-500 animate-pulse',
              botStatus === 'stopping' && 'bg-yellow-500 animate-pulse',
              botStatus === 'error' && 'bg-danger-500 animate-pulse'
            )} />
            <span className="text-sm text-dark-300 capitalize">
              {botStatus}
            </span>
          </div>
        </div>

        {/* Center - Current Asset and Market Data */}
        <div className="flex items-center gap-6">
          {selectedAsset && (
            <div className="text-center">
              <div className="text-xs text-dark-400">Asset</div>
              <div className="text-sm font-medium">{selectedAsset.name}</div>
            </div>
          )}

          {marketData && (
            <>
              <div className="text-center">
                <div className="text-xs text-dark-400">Price</div>
                <div className="text-sm font-medium font-mono">
                  {marketData.price.toFixed(5)}
                </div>
              </div>

              <div className="text-center">
                <div className="text-xs text-dark-400">Trend</div>
                <div className={clsx(
                  'text-sm font-medium capitalize',
                  marketData.trend === 'up' && 'text-success-400',
                  marketData.trend === 'down' && 'text-danger-400',
                  marketData.trend === 'sideways' && 'text-yellow-400'
                )}>
                  {marketData.trend}
                </div>
              </div>

              <div className="text-center">
                <div className="text-xs text-dark-400">Confidence</div>
                <div className={clsx(
                  'text-sm font-medium',
                  marketData.confidence >= 87 ? 'text-success-400' :
                  marketData.confidence >= 60 ? 'text-yellow-400' : 'text-danger-400'
                )}>
                  {marketData.confidence.toFixed(1)}%
                </div>
              </div>
            </>
          )}
        </div>

        {/* Right side - Session info and time */}
        <div className="flex items-center gap-4">
          {/* Session Duration */}
          {currentSession && (
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-dark-400" />
              <div className="text-right">
                <div className="text-xs text-dark-400">Session</div>
                <div className="text-sm font-medium">{getSessionDuration()}</div>
              </div>
            </div>
          )}

          {/* Session Stats */}
          {currentSession && currentSession.totalTrades > 0 && (
            <div className="text-right">
              <div className="text-xs text-dark-400">Trades</div>
              <div className="text-sm font-medium">
                {currentSession.winningTrades}/{currentSession.totalTrades}
              </div>
            </div>
          )}

          {/* Current Time */}
          <div className="text-right">
            <div className="text-xs text-dark-400">Time</div>
            <div className="text-sm font-medium font-mono">
              {formatTime(Date.now())}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
