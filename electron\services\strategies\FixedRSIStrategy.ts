import { BaseStrategyImpl } from '../BaseStrategy'
import { MarketData, TradeDecision, FixedRSIConfig } from '../../../src/types'

export class FixedRSIStrategy extends BaseStrategyImpl {
	private config: FixedRSIConfig
	private lastSignal: 'buy' | 'sell' | 'hold' = 'hold'
	private signalStrength: number = 0

	constructor(config: FixedRSIConfig) {
		super(
			'Fixed RSI',
			'Fixed stake with RSI and EMA indicators. Conservative approach with technical analysis.',
			config
		)
		this.config = config
		this.currentAmount = config.amount
	}

	async execute(marketData: MarketData): Promise<TradeDecision> {
		const confidence = this.calculateConfidence(marketData)

		// Analyze market signals
		const signals = this.analyzeSignals(marketData)
		const direction = this.determineDirectionFromSignals(signals)

		// Fixed amount for this strategy
		const tradeAmount = this.config.amount

		// Only trade if we have a strong signal
		if (signals.strength < 0.6) {
			return {
				action: 'hold',
				amount: 0,
				confidence,
				reasoning: `Signal strength too weak: ${(signals.strength * 100).toFixed(1)}%`,
				metadata: {
					signals,
					rsi: marketData.rsi,
					emaShort: marketData.emaShort,
					emaLong: marketData.emaLong
				}
			}
		}

		this.lastSignal = direction === 'high' ? 'buy' : 'sell'
		this.signalStrength = signals.strength

		return {
			action: this.lastSignal,
			amount: tradeAmount,
			confidence,
			reasoning: this.generateReasoning(marketData, direction, signals),
			metadata: {
				strategy: 'fixed-rsi',
				direction,
				signals,
				rsiPeriod: this.config.rsiPeriod,
				amount: this.config.amount
			}
		}
	}

	private analyzeSignals(marketData: MarketData): {
		rsiSignal: 'buy' | 'sell' | 'neutral'
		emaSignal: 'buy' | 'sell' | 'neutral'
		trendSignal: 'buy' | 'sell' | 'neutral'
		volatilitySignal: 'favorable' | 'unfavorable'
		strength: number
	} {
		// RSI Signal Analysis
		let rsiSignal: 'buy' | 'sell' | 'neutral' = 'neutral'
		if (marketData.rsi <= this.config.oversoldThreshold) {
			rsiSignal = 'buy'
		} else if (marketData.rsi >= this.config.overboughtThreshold) {
			rsiSignal = 'sell'
		}

		// EMA Signal Analysis
		let emaSignal: 'buy' | 'sell' | 'neutral' = 'neutral'
		const emaDiff = marketData.emaShort - marketData.emaLong
		const emaDiffPercent = (emaDiff / marketData.emaLong) * 100

		if (emaDiffPercent > 0.1) {
			emaSignal = 'buy'
		} else if (emaDiffPercent < -0.1) {
			emaSignal = 'sell'
		}

		// Trend Signal Analysis
		let trendSignal: 'buy' | 'sell' | 'neutral' = 'neutral'
		if (marketData.trend === 'up') {
			trendSignal = 'buy'
		} else if (marketData.trend === 'down') {
			trendSignal = 'sell'
		}

		// Volatility Analysis
		const volatilitySignal = marketData.volatility < 0.03 ? 'favorable' : 'unfavorable'

		// Calculate overall signal strength
		const strength = this.calculateSignalStrength(rsiSignal, emaSignal, trendSignal, volatilitySignal)

		return {
			rsiSignal,
			emaSignal,
			trendSignal,
			volatilitySignal,
			strength
		}
	}

	private calculateSignalStrength(
		rsiSignal: string,
		emaSignal: string,
		trendSignal: string,
		volatilitySignal: string
	): number {
		let strength = 0
		let maxStrength = 0

		// RSI weight: 40%
		maxStrength += 0.4
		if (rsiSignal !== 'neutral') {
			strength += 0.4
		}

		// EMA weight: 30%
		maxStrength += 0.3
		if (emaSignal !== 'neutral') {
			strength += 0.3
		}

		// Trend weight: 20%
		maxStrength += 0.2
		if (trendSignal !== 'neutral') {
			strength += 0.2
		}

		// Volatility weight: 10%
		maxStrength += 0.1
		if (volatilitySignal === 'favorable') {
			strength += 0.1
		}

		return strength / maxStrength
	}

	private determineDirectionFromSignals(signals: any): 'high' | 'low' {
		let score = 0

		// RSI signals (strongest weight)
		if (signals.rsiSignal === 'buy') {
			score += 3
		} else if (signals.rsiSignal === 'sell') {
			score -= 3
		}

		// EMA signals
		if (signals.emaSignal === 'buy') {
			score += 2
		} else if (signals.emaSignal === 'sell') {
			score -= 2
		}

		// Trend signals
		if (signals.trendSignal === 'buy') {
			score += 1
		} else if (signals.trendSignal === 'sell') {
			score -= 1
		}

		return score > 0 ? 'high' : 'low'
	}

	private generateReasoning(marketData: MarketData, direction: 'high' | 'low', signals: any): string {
		const reasons = []

		// RSI reasoning
		if (signals.rsiSignal === 'buy') {
			reasons.push(`RSI oversold at ${marketData.rsi.toFixed(1)}`)
		} else if (signals.rsiSignal === 'sell') {
			reasons.push(`RSI overbought at ${marketData.rsi.toFixed(1)}`)
		}

		// EMA reasoning
		if (signals.emaSignal === 'buy') {
			reasons.push('EMA bullish crossover')
		} else if (signals.emaSignal === 'sell') {
			reasons.push('EMA bearish crossover')
		}

		// Trend reasoning
		if (signals.trendSignal !== 'neutral') {
			reasons.push(`${marketData.trend} trend confirmed`)
		}

		// Volatility reasoning
		if (signals.volatilitySignal === 'favorable') {
			reasons.push('low volatility environment')
		}

		const strengthPercent = (signals.strength * 100).toFixed(1)

		return `${direction.toUpperCase()} signal (${strengthPercent}% strength): ${reasons.join(', ')}`
	}

	// Enhanced confidence calculation for RSI strategy
	calculateConfidence(marketData: MarketData): number {
		let confidence = super.calculateConfidence(marketData)

		// RSI-specific confidence adjustments
		const rsiDistance = Math.min(
			Math.abs(marketData.rsi - this.config.oversoldThreshold),
			Math.abs(marketData.rsi - this.config.overboughtThreshold)
		)

		// Higher confidence when RSI is closer to extreme levels
		if (rsiDistance < 10) {
			confidence += 15
		} else if (rsiDistance < 20) {
			confidence += 10
		}

		// EMA divergence confidence
		const emaDivergence = Math.abs(marketData.emaShort - marketData.emaLong) / marketData.emaLong
		if (emaDivergence > 0.002) {
			confidence += 10
		}

		// Consistency bonus
		if (this.consecutiveWins > 2) {
			confidence += 5
		}

		return Math.max(0, Math.min(confidence, 100))
	}

	reset(): void {
		super.reset()
		this.lastSignal = 'hold'
		this.signalStrength = 0
		this.currentAmount = this.config.amount
	}

	// Method to be called after each trade result
	onTradeResult(won: boolean): void {
		this.updateStats(won)
	}

	// Get current strategy state
	getState(): {
		amount: number
		lastSignal: string
		signalStrength: number
		rsiPeriod: number
		oversoldThreshold: number
		overboughtThreshold: number
		consecutiveWins: number
		consecutiveLosses: number
	} {
		return {
			amount: this.config.amount,
			lastSignal: this.lastSignal,
			signalStrength: this.signalStrength,
			rsiPeriod: this.config.rsiPeriod,
			oversoldThreshold: this.config.oversoldThreshold,
			overboughtThreshold: this.config.overboughtThreshold,
			consecutiveWins: this.consecutiveWins,
			consecutiveLosses: this.consecutiveLosses
		}
	}
}
