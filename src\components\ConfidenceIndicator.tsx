import React from 'react'
import { clsx } from 'clsx'

interface ConfidenceIndicatorProps {
  confidence: number
  threshold?: number
  showThreshold?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export const ConfidenceIndicator: React.FC<ConfidenceIndicatorProps> = ({
  confidence,
  threshold = 87,
  showThreshold = true,
  size = 'md'
}) => {
  const getConfidenceLevel = () => {
    if (confidence >= threshold) return 'high'
    if (confidence >= 60) return 'medium'
    return 'low'
  }

  const getConfidenceColor = () => {
    const level = getConfidenceLevel()
    switch (level) {
      case 'high':
        return 'confidence-high'
      case 'medium':
        return 'confidence-medium'
      default:
        return 'confidence-low'
    }
  }

  const getBarHeight = () => {
    switch (size) {
      case 'sm':
        return 'h-1'
      case 'lg':
        return 'h-3'
      default:
        return 'h-2'
    }
  }

  const getTextSize = () => {
    switch (size) {
      case 'sm':
        return 'text-xs'
      case 'lg':
        return 'text-base'
      default:
        return 'text-sm'
    }
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className={clsx('font-medium text-dark-200', getTextSize())}>
          Confidence Level
        </span>
        <span className={clsx('font-bold', getTextSize())}>
          {confidence.toFixed(1)}%
        </span>
      </div>
      
      <div className={clsx('confidence-bar', getBarHeight())}>
        <div 
          className={clsx('confidence-fill', getConfidenceColor(), getBarHeight())}
          style={{ width: `${Math.min(confidence, 100)}%` }}
        />
        
        {/* Threshold indicator */}
        {showThreshold && (
          <div 
            className="absolute top-0 w-0.5 bg-white opacity-75"
            style={{ 
              left: `${threshold}%`,
              height: '100%'
            }}
          />
        )}
      </div>
      
      <div className="flex items-center justify-between text-xs text-dark-400">
        <span>
          {getConfidenceLevel().charAt(0).toUpperCase() + getConfidenceLevel().slice(1)} confidence
        </span>
        
        {showThreshold && (
          <span>
            Threshold: {threshold}%
          </span>
        )}
      </div>
      
      {confidence >= threshold ? (
        <div className="text-xs text-success-400 font-medium">
          ✓ Above threshold - Trading enabled
        </div>
      ) : (
        <div className="text-xs text-yellow-400 font-medium">
          ⚠ Below threshold - Trading paused
        </div>
      )}
    </div>
  )
}
