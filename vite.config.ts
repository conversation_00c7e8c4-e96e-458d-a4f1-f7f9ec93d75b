import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import electron from 'vite-plugin-electron'
import renderer from 'vite-plugin-electron-renderer'
import { resolve } from 'path'

export default defineConfig({
	plugins: [
		react(),
		electron([
			{
				entry: 'electron/main.ts',
				onstart(options) {
					if (options.startup) {
						options.startup()
					}
				},
				vite: {
					build: {
						sourcemap: true,
						minify: false,
						outDir: 'dist-electron',
						rollupOptions: {
							external: ['electron', 'puppeteer', 'playwright', 'tesseract.js', 'ws', 'axios', 'lodash', 'date-fns'],
							output: {
								format: 'es'
							}
						},
						target: 'node18'
					}
				}
			},
			{
				entry: 'electron/preload.ts',
				onstart(options) {
					options.reload()
				},
				vite: {
					build: {
						sourcemap: 'inline',
						minify: false,
						outDir: 'dist-electron',
						rollupOptions: {
							external: ['electron', 'puppeteer', 'playwright', 'tesseract.js', 'ws', 'axios', 'lodash', 'date-fns'],
							output: {
								format: 'es'
							}
						},
						target: 'node18'
					}
				}
			}
		]),
		renderer()
	],
	resolve: {
		alias: {
			'@': resolve(__dirname, 'src'),
			'@electron': resolve(__dirname, 'electron')
		}
	},
	build: {
		outDir: 'dist',
		emptyOutDir: true
	},
	server: {
		port: 5173,
		host: true
	}
})
