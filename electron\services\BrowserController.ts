import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer'
import { chromium } from 'playwright'
import { join } from 'path'
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs'
import { Asset, AuthCredentials, BrowserConfig, ChartCoordinates } from '../../src/types'

export class BrowserController {
  private browser: Browser | null = null
  private page: Page | null = null
  private config: BrowserConfig
  private userDataDir: string
  private cookiesPath: string
  private usePuppeteer: boolean = true

  constructor(config: BrowserConfig) {
    this.config = config
    this.userDataDir = join(process.cwd(), 'browser-data')
    this.cookiesPath = join(this.userDataDir, 'cookies.json')
    
    // Ensure user data directory exists
    if (!existsSync(this.userDataDir)) {
      mkdirSync(this.userDataDir, { recursive: true })
    }
  }

  async initialize(): Promise<void> {
    try {
      await this.launchBrowser()
      await this.setupPage()
      await this.loadCookies()
    } catch (error) {
      console.error('Failed to initialize browser with <PERSON><PERSON>pet<PERSON>, trying Playwright:', error)
      this.usePuppeteer = false
      await this.launchBrowserPlaywright()
    }
  }

  private async launchBrowser(): Promise<void> {
    this.browser = await puppeteer.launch({
      headless: this.config.headless,
      devtools: this.config.devtools,
      userDataDir: this.userDataDir,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--window-size=1920,1080'
      ]
    })
  }

  private async launchBrowserPlaywright(): Promise<void> {
    const browser = await chromium.launch({
      headless: this.config.headless,
      devtools: this.config.devtools,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage'
      ]
    })
    
    // Convert Playwright browser to Puppeteer-like interface
    this.browser = browser as any
  }

  private async setupPage(): Promise<void> {
    if (!this.browser) {
      throw new Error('Browser not initialized')
    }

    const pages = await this.browser.pages()
    this.page = pages.length > 0 ? pages[0] : await this.browser.newPage()

    await this.page.setViewport({
      width: this.config.viewport.width,
      height: this.config.viewport.height
    })

    // Set user agent to avoid detection
    await this.page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    )

    // Block unnecessary resources to speed up loading
    await this.page.setRequestInterception(true)
    this.page.on('request', (req) => {
      const resourceType = req.resourceType()
      if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
        req.abort()
      } else {
        req.continue()
      }
    })
  }

  async navigateToPocketOption(): Promise<void> {
    if (!this.page) {
      throw new Error('Page not initialized')
    }

    const url = process.env.POCKET_OPTION_URL || 'https://pocketoption.com/en/cabinet/demo-quick-high-low/'
    
    await this.page.goto(url, {
      waitUntil: 'networkidle2',
      timeout: 30000
    })

    // Wait for the trading interface to load
    await this.page.waitForSelector('.trading-panel', { timeout: 15000 })
  }

  async authenticate(credentials: AuthCredentials): Promise<{ success: boolean; message?: string }> {
    try {
      if (!this.page) {
        throw new Error('Page not initialized')
      }

      // Check if already logged in
      const isLoggedIn = await this.checkIfLoggedIn()
      if (isLoggedIn) {
        return { success: true, message: 'Already authenticated' }
      }

      // If we have session cookies, try to use them first
      if (credentials.sessionCookies) {
        await this.loadCookiesFromString(credentials.sessionCookies)
        await this.page.reload({ waitUntil: 'networkidle2' })
        
        const stillLoggedIn = await this.checkIfLoggedIn()
        if (stillLoggedIn) {
          await this.saveCookies()
          return { success: true, message: 'Authenticated with saved session' }
        }
      }

      // Manual login if credentials provided
      if (credentials.email && credentials.password) {
        await this.performLogin(credentials.email, credentials.password)
        await this.saveCookies()
        return { success: true, message: 'Login successful' }
      }

      return { success: false, message: 'No valid authentication method provided' }
    } catch (error) {
      console.error('Authentication failed:', error)
      return { success: false, message: error.message }
    }
  }

  private async checkIfLoggedIn(): Promise<boolean> {
    try {
      if (!this.page) return false
      
      // Look for elements that indicate user is logged in
      const loginIndicators = [
        '.user-menu',
        '.account-balance',
        '.trading-panel .user-info',
        '[data-testid="user-avatar"]'
      ]

      for (const selector of loginIndicators) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2000 })
          return true
        } catch {
          continue
        }
      }

      return false
    } catch {
      return false
    }
  }

  private async performLogin(email: string, password: string): Promise<void> {
    if (!this.page) {
      throw new Error('Page not initialized')
    }

    // Click login button to open login form
    await this.page.click('.login-btn, .sign-in-btn, [data-testid="login-button"]')
    
    // Wait for login form
    await this.page.waitForSelector('input[type="email"], input[name="email"]', { timeout: 10000 })
    
    // Fill in credentials
    await this.page.type('input[type="email"], input[name="email"]', email)
    await this.page.type('input[type="password"], input[name="password"]', password)
    
    // Submit login form
    await this.page.click('button[type="submit"], .login-submit, [data-testid="login-submit"]')
    
    // Wait for login to complete
    await this.page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 })
    
    // Verify login was successful
    const isLoggedIn = await this.checkIfLoggedIn()
    if (!isLoggedIn) {
      throw new Error('Login verification failed')
    }
  }

  async getAvailableAssets(): Promise<Asset[]> {
    if (!this.page) {
      throw new Error('Page not initialized')
    }

    try {
      // Wait for asset list to load
      await this.page.waitForSelector('.assets-list, .asset-item, [data-testid="asset-list"]', { timeout: 10000 })

      // Extract asset information from DOM
      const assets = await this.page.evaluate(() => {
        const assetElements = document.querySelectorAll('.asset-item, [data-asset-id]')
        const assets: Asset[] = []

        assetElements.forEach((element, index) => {
          const nameElement = element.querySelector('.asset-name, .symbol')
          const categoryElement = element.querySelector('.asset-category, .category')
          const payoutElement = element.querySelector('.payout, .profit')
          
          if (nameElement) {
            const name = nameElement.textContent?.trim() || `Asset ${index + 1}`
            const symbol = name.split('/')[0] || name
            const category = categoryElement?.textContent?.trim() || 'currency'
            const payoutText = payoutElement?.textContent?.trim() || '80%'
            const payout = parseInt(payoutText.replace('%', '')) || 80

            assets.push({
              id: `asset_${index}`,
              name,
              symbol,
              category: category.toLowerCase(),
              type: 'OTC',
              isActive: !element.classList.contains('disabled'),
              minTradeAmount: 1,
              maxTradeAmount: 100,
              payoutPercentage: payout
            })
          }
        })

        return assets.slice(0, 10) // Limit to 10 assets as requested
      })

      return assets
    } catch (error) {
      console.error('Failed to get assets:', error)
      return []
    }
  }

  async selectAsset(assetName: string): Promise<void> {
    if (!this.page) {
      throw new Error('Page not initialized')
    }

    // Find and click the asset
    await this.page.evaluate((name) => {
      const assetElements = document.querySelectorAll('.asset-item, [data-asset-id]')
      
      for (const element of assetElements) {
        const nameElement = element.querySelector('.asset-name, .symbol')
        if (nameElement?.textContent?.includes(name)) {
          (element as HTMLElement).click()
          break
        }
      }
    }, assetName)

    // Wait for asset to be selected
    await this.page.waitForTimeout(1000)
  }

  async setTimePeriod(period: string): Promise<void> {
    if (!this.page) {
      throw new Error('Page not initialized')
    }

    // Find and click the time period selector
    const timeSelectors = [
      `[data-time="${period}"]`,
      `.time-${period.toLowerCase()}`,
      `.period-${period.toLowerCase()}`
    ]

    for (const selector of timeSelectors) {
      try {
        await this.page.click(selector)
        await this.page.waitForTimeout(500)
        return
      } catch {
        continue
      }
    }

    // Fallback: try to find by text content
    await this.page.evaluate((period) => {
      const elements = document.querySelectorAll('.time-period, .period-selector button, .time-btn')
      
      for (const element of elements) {
        if (element.textContent?.includes(period)) {
          (element as HTMLElement).click()
          break
        }
      }
    }, period)
  }

  async setTradeAmount(amount: number): Promise<void> {
    if (!this.page) {
      throw new Error('Page not initialized')
    }

    // Find amount input field
    const amountSelectors = [
      'input[name="amount"]',
      '.amount-input input',
      '[data-testid="trade-amount"] input',
      '.trade-amount input'
    ]

    for (const selector of amountSelectors) {
      try {
        await this.page.click(selector)
        await this.page.keyboard.selectAll()
        await this.page.type(selector, amount.toString())
        return
      } catch {
        continue
      }
    }

    throw new Error('Could not find trade amount input field')
  }

  async executeTrade(direction: 'high' | 'low'): Promise<{ success: boolean; message?: string }> {
    try {
      if (!this.page) {
        throw new Error('Page not initialized')
      }

      const buttonSelector = direction === 'high' 
        ? '.btn-higher, .call-btn, [data-testid="call-button"], .green-btn'
        : '.btn-lower, .put-btn, [data-testid="put-button"], .red-btn'

      await this.page.click(buttonSelector)
      
      // Wait for trade confirmation
      await this.page.waitForTimeout(2000)
      
      // Check if trade was successful
      const tradeSuccess = await this.page.evaluate(() => {
        const successIndicators = [
          '.trade-success',
          '.position-opened',
          '.trade-confirmation'
        ]
        
        return successIndicators.some(selector => 
          document.querySelector(selector) !== null
        )
      })

      return {
        success: tradeSuccess,
        message: tradeSuccess ? 'Trade executed successfully' : 'Trade execution may have failed'
      }
    } catch (error) {
      console.error('Trade execution failed:', error)
      return { success: false, message: error.message }
    }
  }

  async captureScreenshot(coordinates?: ChartCoordinates): Promise<Buffer> {
    if (!this.page) {
      throw new Error('Page not initialized')
    }

    if (coordinates) {
      return await this.page.screenshot({
        clip: {
          x: coordinates.x,
          y: coordinates.y,
          width: coordinates.width,
          height: coordinates.height
        }
      })
    }

    return await this.page.screenshot({ fullPage: false })
  }

  private async saveCookies(): Promise<void> {
    if (!this.page) return

    try {
      const cookies = await this.page.cookies()
      writeFileSync(this.cookiesPath, JSON.stringify(cookies, null, 2))
    } catch (error) {
      console.error('Failed to save cookies:', error)
    }
  }

  private async loadCookies(): Promise<void> {
    if (!this.page || !existsSync(this.cookiesPath)) return

    try {
      const cookiesData = readFileSync(this.cookiesPath, 'utf8')
      const cookies = JSON.parse(cookiesData)
      await this.page.setCookie(...cookies)
    } catch (error) {
      console.error('Failed to load cookies:', error)
    }
  }

  private async loadCookiesFromString(cookiesString: string): Promise<void> {
    if (!this.page) return

    try {
      const cookies = JSON.parse(cookiesString)
      await this.page.setCookie(...cookies)
    } catch (error) {
      console.error('Failed to load cookies from string:', error)
    }
  }

  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      this.browser = null
      this.page = null
    }
  }
}
