import { app, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { TradingBot } from './services/TradingBot'
import { StrategyManager } from './services/StrategyManager'
import { <PERSON>rowserController } from './services/BrowserController'

// Handle __dirname in ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null
let tradingBot: TradingBot | null = null

const isDev = process.env.NODE_ENV === 'development'
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL

function createWindow() {
	mainWindow = new BrowserWindow({
		width: 1400,
		height: 900,
		minWidth: 1200,
		minHeight: 800,
		webPreferences: {
			preload: join(__dirname, 'preload.js'),
			nodeIntegration: false,
			contextIsolation: true,
			enableRemoteModule: false,
			webSecurity: true
		},
		titleBarStyle: 'default',
		backgroundColor: '#0f172a',
		show: false,
		icon: join(__dirname, '../assets/icon.png')
	})

	// Load the app
	if (VITE_DEV_SERVER_URL) {
		mainWindow.loadURL(VITE_DEV_SERVER_URL)
		if (isDev) {
			mainWindow.webContents.openDevTools()
		}
	} else {
		mainWindow.loadFile(join(__dirname, '../dist/index.html'))
	}

	mainWindow.once('ready-to-show', () => {
		mainWindow?.show()
	})

	mainWindow.on('closed', () => {
		mainWindow = null
		if (tradingBot) {
			tradingBot.stop()
			tradingBot = null
		}
	})
}

// App event listeners
app.whenReady().then(() => {
	createWindow()

	app.on('activate', () => {
		if (BrowserWindow.getAllWindows().length === 0) {
			createWindow()
		}
	})
})

app.on('window-all-closed', () => {
	if (process.platform !== 'darwin') {
		app.quit()
	}
})

// IPC Handlers
ipcMain.handle('bot:initialize', async (_, config) => {
	try {
		if (tradingBot) {
			await tradingBot.stop()
		}

		tradingBot = new TradingBot(config)
		await tradingBot.initialize()

		return { success: true, message: 'Bot initialized successfully' }
	} catch (error) {
		console.error('Failed to initialize bot:', error)
		return { success: false, message: error.message }
	}
})

ipcMain.handle('bot:start', async () => {
	try {
		if (!tradingBot) {
			throw new Error('Bot not initialized')
		}

		await tradingBot.start()
		return { success: true, message: 'Bot started successfully' }
	} catch (error) {
		console.error('Failed to start bot:', error)
		return { success: false, message: error.message }
	}
})

ipcMain.handle('bot:stop', async () => {
	try {
		if (tradingBot) {
			await tradingBot.stop()
		}
		return { success: true, message: 'Bot stopped successfully' }
	} catch (error) {
		console.error('Failed to stop bot:', error)
		return { success: false, message: error.message }
	}
})

ipcMain.handle('bot:status', async () => {
	if (!tradingBot) {
		return { status: 'stopped', data: null }
	}

	return {
		status: tradingBot.getStatus(),
		data: tradingBot.getStatusData()
	}
})

ipcMain.handle('browser:authenticate', async (_, credentials) => {
	try {
		if (!tradingBot) {
			throw new Error('Bot not initialized')
		}

		const result = await tradingBot.authenticate(credentials)
		return result
	} catch (error) {
		console.error('Authentication failed:', error)
		return { success: false, message: error.message }
	}
})

ipcMain.handle('browser:getAssets', async () => {
	try {
		if (!tradingBot) {
			throw new Error('Bot not initialized')
		}

		const assets = await tradingBot.getAvailableAssets()
		return { success: true, assets }
	} catch (error) {
		console.error('Failed to get assets:', error)
		return { success: false, message: error.message }
	}
})

ipcMain.handle('dialog:selectScreenshotArea', async () => {
	try {
		const result = await dialog.showMessageBox(mainWindow!, {
			type: 'info',
			title: 'Screenshot Area Selection',
			message: 'Click OK and then select the chart area on the Pocket Option page',
			buttons: ['OK', 'Cancel']
		})

		if (result.response === 0) {
			// User clicked OK, now capture screenshot coordinates
			if (tradingBot) {
				const coordinates = await tradingBot.captureChartCoordinates()
				return { success: true, coordinates }
			}
		}

		return { success: false, message: 'Screenshot selection cancelled' }
	} catch (error) {
		console.error('Failed to select screenshot area:', error)
		return { success: false, message: error.message }
	}
})

// Strategy management
ipcMain.handle('strategy:getAvailable', async () => {
	const strategies = StrategyManager.getAvailableStrategies()
	return { success: true, strategies }
})

ipcMain.handle('strategy:setActive', async (_, strategyName, config) => {
	try {
		if (!tradingBot) {
			throw new Error('Bot not initialized')
		}

		await tradingBot.setStrategy(strategyName, config)
		return { success: true, message: `Strategy ${strategyName} activated` }
	} catch (error) {
		console.error('Failed to set strategy:', error)
		return { success: false, message: error.message }
	}
})

// Error handling
process.on('uncaughtException', error => {
	console.error('Uncaught Exception:', error)
})

process.on('unhandledRejection', (reason, promise) => {
	console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})
