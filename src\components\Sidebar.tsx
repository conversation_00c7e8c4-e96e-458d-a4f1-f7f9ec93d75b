import React from 'react'
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  History, 
  Play, 
  Square, 
  Shield,
  TrendingUp,
  Zap,
  User
} from 'lucide-react'
import { useBotStore, useIsRunning, useCanStart } from '../store/useBotStore'
import { clsx } from 'clsx'

export const Sidebar: React.FC = () => {
  const { 
    activeTab, 
    setActiveTab, 
    botStatus, 
    isAuthenticated,
    isConnected,
    addNotification
  } = useBotStore()
  
  const isRunning = useIsRunning()
  const canStart = useCanStart()

  const handleStartBot = async () => {
    if (!window.electronAPI) {
      addNotification({
        type: 'error',
        title: 'Not Connected',
        message: 'Electron API not available',
        autoClose: true
      })
      return
    }

    try {
      const result = await window.electronAPI.bot.start()
      if (!result.success) {
        addNotification({
          type: 'error',
          title: 'Failed to Start Bot',
          message: result.message,
          autoClose: true
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to start bot',
        autoClose: true
      })
    }
  }

  const handleStopBot = async () => {
    if (!window.electronAPI) return

    try {
      const result = await window.electronAPI.bot.stop()
      if (!result.success) {
        addNotification({
          type: 'error',
          title: 'Failed to Stop Bot',
          message: result.message,
          autoClose: true
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to stop bot',
        autoClose: true
      })
    }
  }

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      description: 'Overview and metrics'
    },
    {
      id: 'config',
      label: 'Configuration',
      icon: Settings,
      description: 'Bot settings and strategy'
    },
    {
      id: 'history',
      label: 'Trade History',
      icon: History,
      description: 'Recent trades and results'
    },
    {
      id: 'auth',
      label: 'Authentication',
      icon: User,
      description: 'Login and account settings'
    }
  ]

  return (
    <div className="w-64 bg-dark-900 border-r border-dark-700 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-dark-700">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary-600 rounded-lg">
            <TrendingUp className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="font-bold text-lg">Pocket Bot</h1>
            <p className="text-xs text-dark-400">Trading Assistant</p>
          </div>
        </div>
      </div>

      {/* Connection Status */}
      <div className="p-4 border-b border-dark-700">
        <div className="flex items-center gap-2 text-sm">
          <div className={clsx(
            'h-2 w-2 rounded-full',
            isConnected ? 'bg-success-500' : 'bg-danger-500'
          )} />
          <span className="text-dark-300">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        
        {isAuthenticated && (
          <div className="flex items-center gap-2 text-sm mt-1">
            <Shield className="h-3 w-3 text-success-500" />
            <span className="text-success-400">Authenticated</span>
          </div>
        )}
      </div>

      {/* Bot Controls */}
      <div className="p-4 border-b border-dark-700">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Bot Status</span>
            <span className={clsx(
              'text-xs px-2 py-1 rounded-full',
              botStatus === 'running' && 'bg-success-900 text-success-100',
              botStatus === 'stopped' && 'bg-dark-700 text-dark-300',
              botStatus === 'starting' && 'bg-yellow-900 text-yellow-100',
              botStatus === 'stopping' && 'bg-yellow-900 text-yellow-100',
              botStatus === 'error' && 'bg-danger-900 text-danger-100'
            )}>
              {botStatus.charAt(0).toUpperCase() + botStatus.slice(1)}
            </span>
          </div>
          
          <div className="flex gap-2">
            {!isRunning ? (
              <button
                onClick={handleStartBot}
                disabled={!canStart}
                className={clsx(
                  'flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-md text-sm font-medium transition-colors',
                  canStart
                    ? 'bg-success-600 hover:bg-success-700 text-white'
                    : 'bg-dark-700 text-dark-400 cursor-not-allowed'
                )}
              >
                <Play className="h-4 w-4" />
                Start
              </button>
            ) : (
              <button
                onClick={handleStopBot}
                className="flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-md text-sm font-medium bg-danger-600 hover:bg-danger-700 text-white transition-colors"
              >
                <Square className="h-4 w-4" />
                Stop
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <div className="space-y-1">
          {navigationItems.map((item) => {
            const Icon = item.icon
            const isActive = activeTab === item.id
            
            return (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={clsx(
                  'w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors text-left',
                  isActive
                    ? 'bg-primary-600 text-white'
                    : 'text-dark-300 hover:text-white hover:bg-dark-700'
                )}
              >
                <Icon className="h-4 w-4 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="truncate">{item.label}</div>
                  {!isActive && (
                    <div className="text-xs text-dark-500 truncate">
                      {item.description}
                    </div>
                  )}
                </div>
              </button>
            )
          })}
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-dark-700">
        <div className="flex items-center gap-2 text-xs text-dark-500">
          <Zap className="h-3 w-3" />
          <span>Powered by Puppeteer & React</span>
        </div>
      </div>
    </div>
  )
}
