import { EventEmitter } from 'events'
import { BrowserController } from './BrowserController'
import { StrategyManager } from './StrategyManager'
import { OCRService } from './OCRService'
import {
	BotConfig,
	Asset,
	AuthCredentials,
	Trade,
	TradingSession,
	MarketData,
	TradeDecision,
	BotEvent,
	ChartCoordinates
} from '../../src/types'

export class TradingBot extends EventEmitter {
	private config: BotConfig
	private browserController: BrowserController
	private strategyManager: StrategyManager
	private ocrService: OCRService

	private status: 'stopped' | 'starting' | 'running' | 'stopping' | 'error' = 'stopped'
	private currentSession: TradingSession | null = null
	private recentTrades: Trade[] = []
	private isAuthenticated: boolean = false
	private availableAssets: Asset[] = []
	private selectedAsset: Asset | null = null
	private chartCoordinates: ChartCoordinates | null = null

	private analysisInterval: NodeJS.Timeout | null = null
	private tradingInterval: NodeJS.Timeout | null = null
	private lastTradeTime: number = 0
	private cooldownPeriod: number = 30000 // 30 seconds cooldown

	constructor(config: BotConfig) {
		super()
		this.config = config

		this.browserController = new BrowserController({
			headless: config.headlessBrowser ?? true, // Default to headless mode
			devtools: config.debugLogs,
			userDataDir: 'browser-data',
			viewport: { width: 1920, height: 1080 }
		})

		this.strategyManager = new StrategyManager()
		this.ocrService = new OCRService()
	}

	async initialize(): Promise<void> {
		try {
			this.setStatus('starting')

			// Initialize services
			await this.browserController.initialize()
			await this.ocrService.initialize()

			// Navigate to Pocket Option
			await this.browserController.navigateToPocketOption()

			// Set up strategy if provided
			if (this.config.strategy) {
				const strategies = StrategyManager.getAvailableStrategies()
				const strategyConfig = strategies.find(s => s.name === this.config.strategy)?.defaultConfig

				if (strategyConfig) {
					this.strategyManager.setStrategy(this.config.strategy, {
						...strategyConfig,
						...this.config
					})
				}
			}

			this.setStatus('stopped')
			this.emit('bot:initialized')
		} catch (error) {
			this.setStatus('error')
			this.emit('bot:error', error)
			throw error
		}
	}

	async authenticate(credentials: AuthCredentials): Promise<{ success: boolean; message?: string }> {
		try {
			const result = await this.browserController.authenticate(credentials)
			this.isAuthenticated = result.success

			if (result.success) {
				// Load available assets after authentication
				this.availableAssets = await this.browserController.getAvailableAssets()
				this.emit('bot:authenticated', { assets: this.availableAssets })
			}

			return result
		} catch (error) {
			console.error('Authentication failed:', error)
			return { success: false, message: error.message }
		}
	}

	async start(): Promise<void> {
		if (!this.isAuthenticated) {
			throw new Error('Bot must be authenticated before starting')
		}

		if (this.status === 'running') {
			throw new Error('Bot is already running')
		}

		try {
			this.setStatus('starting')

			// Initialize trading session
			this.currentSession = {
				startTime: Date.now(),
				totalTrades: 0,
				winningTrades: 0,
				losingTrades: 0,
				totalProfit: 0,
				winRate: 0,
				maxDrawdown: 0,
				currentStreak: 0,
				bestStreak: 0,
				worstStreak: 0
			}

			// Set up selected asset
			if (this.config.assetFilter.selectedAssets.length > 0) {
				const assetName = this.config.assetFilter.selectedAssets[0]
				await this.browserController.selectAsset(assetName)
				this.selectedAsset = this.availableAssets.find(a => a.name === assetName) || null
			}

			// Set time period
			await this.browserController.setTimePeriod(this.config.timePeriod)

			// Start analysis and trading loops
			this.startAnalysisLoop()
			this.startTradingLoop()

			this.setStatus('running')
			this.emit('bot:started')
		} catch (error) {
			this.setStatus('error')
			this.emit('bot:error', error)
			throw error
		}
	}

	async stop(): Promise<void> {
		this.setStatus('stopping')

		// Clear intervals
		if (this.analysisInterval) {
			clearInterval(this.analysisInterval)
			this.analysisInterval = null
		}

		if (this.tradingInterval) {
			clearInterval(this.tradingInterval)
			this.tradingInterval = null
		}

		// Finalize session
		if (this.currentSession) {
			this.currentSession.endTime = Date.now()
			this.emit('bot:session_ended', this.currentSession)
		}

		this.setStatus('stopped')
		this.emit('bot:stopped')
	}

	private startAnalysisLoop(): void {
		this.analysisInterval = setInterval(async () => {
			try {
				if (this.status !== 'running') return

				await this.performAnalysis()
			} catch (error) {
				console.error('Analysis loop error:', error)
				this.emit('bot:error', error)
			}
		}, 1000) // 1 second interval for chart analysis
	}

	private startTradingLoop(): void {
		this.tradingInterval = setInterval(async () => {
			try {
				if (this.status !== 'running') return

				// Check cooldown
				const timeSinceLastTrade = Date.now() - this.lastTradeTime
				if (timeSinceLastTrade < this.cooldownPeriod) {
					return
				}

				await this.evaluateAndExecuteTrade()
			} catch (error) {
				console.error('Trading loop error:', error)
				this.emit('bot:error', error)
			}
		}, 5000) // 5 second interval for trading decisions (configurable)
	}

	private async performAnalysis(): Promise<void> {
		if (!this.chartCoordinates) {
			return // No chart coordinates set
		}

		try {
			// Capture chart screenshot
			const screenshot = await this.browserController.captureScreenshot(this.chartCoordinates)

			// Analyze chart with OCR
			const chartAnalysis = await this.ocrService.analyzeChart(screenshot, this.chartCoordinates)

			// Convert to market data
			const marketData = this.ocrService.chartAnalysisToMarketData(chartAnalysis, this.selectedAsset?.name || 'Unknown')

			// Emit market data update
			this.emit('bot:market_data', marketData)
		} catch (error) {
			console.error('Chart analysis failed:', error)
		}
	}

	private async evaluateAndExecuteTrade(): Promise<void> {
		if (!this.selectedAsset || !this.chartCoordinates) {
			return
		}

		try {
			// Get latest market data
			const screenshot = await this.browserController.captureScreenshot(this.chartCoordinates)
			const chartAnalysis = await this.ocrService.analyzeChart(screenshot, this.chartCoordinates)
			const marketData = this.ocrService.chartAnalysisToMarketData(chartAnalysis, this.selectedAsset.name)

			// Execute strategy
			const decision = await this.strategyManager.executeStrategy(marketData)

			if (!decision || decision.action === 'hold') {
				return
			}

			// Check confidence threshold
			if (decision.confidence < this.config.confidenceThreshold) {
				this.emit('bot:trade_skipped', {
					reason: 'Confidence below threshold',
					confidence: decision.confidence,
					threshold: this.config.confidenceThreshold
				})
				return
			}

			// Execute trade if not in dry run mode
			if (!this.config.dryRun) {
				await this.executeTrade(decision, marketData)
			} else {
				this.logDryRunTrade(decision, marketData)
			}
		} catch (error) {
			console.error('Trade evaluation failed:', error)
		}
	}

	private async executeTrade(decision: TradeDecision, marketData: MarketData): Promise<void> {
		try {
			// Set trade amount
			await this.browserController.setTradeAmount(decision.amount)

			// Execute trade
			const direction = decision.action === 'buy' ? 'high' : 'low'
			const result = await this.browserController.executeTrade(direction)

			if (result.success) {
				// Create trade record
				const trade: Trade = {
					id: `trade_${Date.now()}`,
					asset: this.selectedAsset!.name,
					direction,
					amount: decision.amount,
					openPrice: marketData.price,
					openTime: Date.now(),
					duration: this.getTimePeriodInMs(this.config.timePeriod),
					strategy: this.strategyManager.getActiveStrategy()?.name || 'unknown',
					confidence: decision.confidence,
					result: 'pending'
				}

				this.recentTrades.unshift(trade)
				this.lastTradeTime = Date.now()

				// Update session stats
				if (this.currentSession) {
					this.currentSession.totalTrades++
				}

				this.emit('bot:trade_opened', trade)

				// Schedule trade result check
				setTimeout(() => {
					this.checkTradeResult(trade.id)
				}, trade.duration + 5000) // Check 5 seconds after expiry
			} else {
				this.emit('bot:trade_failed', { decision, error: result.message })
			}
		} catch (error) {
			console.error('Trade execution failed:', error)
			this.emit('bot:trade_failed', { decision, error: error.message })
		}
	}

	private logDryRunTrade(decision: TradeDecision, marketData: MarketData): void {
		const trade: Trade = {
			id: `dry_run_${Date.now()}`,
			asset: this.selectedAsset!.name,
			direction: decision.action === 'buy' ? 'high' : 'low',
			amount: decision.amount,
			openPrice: marketData.price,
			openTime: Date.now(),
			duration: this.getTimePeriodInMs(this.config.timePeriod),
			strategy: this.strategyManager.getActiveStrategy()?.name || 'unknown',
			confidence: decision.confidence,
			result: 'pending'
		}

		this.recentTrades.unshift(trade)
		this.emit('bot:dry_run_trade', trade)
	}

	private async checkTradeResult(tradeId: string): Promise<void> {
		// Implementation would check the actual trade result from the platform
		// For now, simulate random result
		const trade = this.recentTrades.find(t => t.id === tradeId)
		if (!trade) return

		// Simulate trade result (in real implementation, this would check the platform)
		const won = Math.random() > 0.5
		trade.result = won ? 'win' : 'loss'
		trade.closeTime = Date.now()
		trade.profit = won ? trade.amount * 0.8 : -trade.amount // 80% payout

		// Update session stats
		if (this.currentSession) {
			if (won) {
				this.currentSession.winningTrades++
				this.currentSession.currentStreak = Math.max(0, this.currentSession.currentStreak + 1)
				this.currentSession.bestStreak = Math.max(this.currentSession.bestStreak, this.currentSession.currentStreak)
			} else {
				this.currentSession.losingTrades++
				this.currentSession.currentStreak = Math.min(0, this.currentSession.currentStreak - 1)
				this.currentSession.worstStreak = Math.min(this.currentSession.worstStreak, this.currentSession.currentStreak)
			}

			this.currentSession.totalProfit += trade.profit
			this.currentSession.winRate = (this.currentSession.winningTrades / this.currentSession.totalTrades) * 100
		}

		// Update strategy stats
		const activeStrategy = this.strategyManager.getActiveStrategy()
		if (activeStrategy && 'onTradeResult' in activeStrategy) {
			;(activeStrategy as any).onTradeResult(won)
		}

		this.emit('bot:trade_closed', trade)
	}

	private getTimePeriodInMs(period: string): number {
		const periodMap: Record<string, number> = {
			S5: 5000,
			S15: 15000,
			S30: 30000,
			M1: 60000,
			M3: 180000,
			M5: 300000,
			M15: 900000,
			M30: 1800000,
			H1: 3600000
		}

		return periodMap[period] || 60000
	}

	// Public methods for external control
	async setStrategy(strategyName: string, config: any): Promise<void> {
		this.strategyManager.setStrategy(strategyName as any, config)
		this.emit('bot:strategy_changed', { strategy: strategyName, config })
	}

	async getAvailableAssets(): Promise<Asset[]> {
		if (this.availableAssets.length === 0) {
			this.availableAssets = await this.browserController.getAvailableAssets()
		}
		return this.availableAssets
	}

	async captureChartCoordinates(): Promise<ChartCoordinates> {
		// This would implement a UI for the user to select chart coordinates
		// For now, return default coordinates
		const coordinates: ChartCoordinates = {
			x: 100,
			y: 100,
			width: 800,
			height: 400
		}

		this.chartCoordinates = coordinates
		return coordinates
	}

	getStatus(): string {
		return this.status
	}

	getStatusData(): any {
		return {
			isAuthenticated: this.isAuthenticated,
			currentSession: this.currentSession,
			recentTrades: this.recentTrades.slice(0, 10), // Last 10 trades
			selectedAsset: this.selectedAsset,
			availableAssets: this.availableAssets,
			chartCoordinates: this.chartCoordinates,
			activeStrategy: this.strategyManager.getActiveStrategy()?.name
		}
	}

	private setStatus(status: typeof this.status): void {
		this.status = status
		this.emit('bot:status_changed', status)
	}

	async cleanup(): Promise<void> {
		await this.stop()
		await this.browserController.close()
		await this.ocrService.cleanup()
	}
}
