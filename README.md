# Pocket Option Trading Bot

A sophisticated trading bot for Pocket Option with a modern Electron GUI, built with React, TypeScript, and advanced trading strategies.

## 🚀 Features

### Core Functionality
- **Browser Automation**: Puppeteer-based automation with Playwright fallback
- **Persistent Authentication**: Cookie-based session management
- **Real-time Analysis**: OCR-powered chart analysis with Tesseract.js
- **Multiple Strategies**: Martingale, D'Alembert, and Fixed RSI strategies
- **Confidence Scoring**: Advanced confidence calculation (87% threshold)
- **Risk Management**: Cooldown periods and position sizing

### GUI Features
- **Dark Theme**: Modern, professional interface
- **Real-time Dashboard**: Live metrics and performance tracking
- **Strategy Configuration**: Easy strategy switching and parameter tuning
- **Trade History**: Comprehensive trade logging and analysis
- **Asset Management**: Automatic asset discovery and filtering
- **Notifications**: Real-time alerts and status updates

### Trading Strategies

#### 1. Martingale Strategy
- Doubles bet after each loss
- Resets to base amount on win
- Configurable max steps and multiplier
- High risk, high reward approach

#### 2. D'Alembert Strategy
- Increases bet by fixed amount after loss
- Decreases bet after win
- More conservative than Martingale
- Better risk management

#### 3. Fixed RSI Strategy
- Uses RSI and EMA indicators
- Fixed stake amount
- Technical analysis based
- Conservative approach with high accuracy

## 🛠️ Technology Stack

- **Frontend**: React 18 + TypeScript + Tailwind CSS
- **Backend**: Electron + Node.js
- **Automation**: Puppeteer (primary) + Playwright (fallback)
- **OCR**: Tesseract.js for chart analysis
- **State Management**: Zustand
- **Build Tool**: Vite
- **Package Manager**: pnpm

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) or npm
- Windows/macOS/Linux

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd pocket-bot-v2

# Install dependencies
pnpm install

# Start development server
pnpm electron:dev
```

## 🚀 Usage

### Development Mode
```bash
# Start the development server with hot reload
pnpm electron:dev

# Build for production
pnpm build

# Package for distribution
pnpm electron:dist
```

### First Time Setup

1. **Launch the Application**
   ```bash
   pnpm electron:dev
   ```

2. **Authentication**
   - Navigate to the Authentication tab
   - Choose between email/password or session cookies
   - Enter your Pocket Option credentials
   - Click "Authenticate"

3. **Configuration**
   - Go to Configuration tab
   - Set your trading parameters:
     - Account type (Demo/Live)
     - Trade amount and capital
     - Strategy selection
     - Time period
     - Confidence threshold
   - Select chart area for analysis
   - Choose assets to trade

4. **Start Trading**
   - Return to Dashboard
   - Click "Start" to begin automated trading
   - Monitor performance in real-time

## ⚙️ Configuration Options

### Basic Settings
- **Account Type**: Demo or Live trading
- **Trade Capital**: Total available capital
- **Target Profit**: Session profit target
- **Trade Amount**: Base trade size

### Strategy Settings
- **Strategy**: Choose from 3 available strategies
- **Time Period**: 5s to 1h timeframes
- **Confidence Threshold**: Minimum confidence to trade (default: 87%)

### Asset Filter
- **Type**: OTC or All assets
- **Category**: Currency, Crypto, Commodities, Stocks, Indices
- **Selected Assets**: Up to 10 assets for trading

### Advanced Options
- **Dry Run Mode**: Test without real money
- **Debug Logs**: Enable detailed logging
- **Chart Coordinates**: Custom chart area selection

## 🔧 Architecture

### Project Structure
```
pocket-bot-v2/
├── electron/                 # Electron main process
│   ├── main.ts              # Main process entry
│   ├── preload.ts           # IPC bridge
│   └── services/            # Core services
│       ├── TradingBot.ts    # Main bot orchestrator
│       ├── BrowserController.ts # Browser automation
│       ├── StrategyManager.ts   # Strategy management
│       ├── OCRService.ts    # Chart analysis
│       └── strategies/      # Trading strategies
├── src/                     # React frontend
│   ├── components/          # UI components
│   ├── store/              # State management
│   ├── types/              # TypeScript definitions
│   └── App.tsx             # Main app component
├── dist/                   # Built frontend
├── dist-electron/          # Built Electron app
└── release/               # Packaged distributions
```

### Key Components

#### TradingBot Service
- Orchestrates all bot functionality
- Manages trading sessions and loops
- Handles strategy execution
- Provides real-time updates via events

#### BrowserController
- Puppeteer/Playwright automation
- Cookie persistence
- DOM manipulation for trading
- Screenshot capture

#### StrategyManager
- Strategy selection and execution
- Confidence calculation
- Trade decision making
- Performance tracking

#### OCRService
- Chart screenshot analysis
- Price and indicator extraction
- Market data generation
- Technical analysis

## 🔒 Security & Safety

### Risk Management
- **Dry Run Mode**: Test strategies without real money
- **Confidence Threshold**: Only trade with high confidence
- **Cooldown Periods**: Prevent overtrading
- **Position Sizing**: Controlled trade amounts
- **Stop Conditions**: Automatic stopping on errors

### Data Security
- **Local Storage**: All data stored locally
- **No External Transmission**: Credentials never leave your machine
- **Session Management**: Secure cookie handling
- **Error Handling**: Comprehensive error recovery

## 📊 Performance Monitoring

### Real-time Metrics
- **Win Rate**: Percentage of winning trades
- **Total Profit**: Session profit/loss
- **Trade Count**: Number of executed trades
- **Confidence Levels**: Current market confidence
- **Strategy Performance**: Individual strategy stats

### Trade History
- **Detailed Logs**: Complete trade information
- **Export Functionality**: CSV export for analysis
- **Filtering**: Filter by result, asset, strategy
- **Performance Analytics**: Win/loss streaks, drawdown

## 🐛 Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify Pocket Option credentials
   - Check internet connection
   - Try session cookie method
   - Clear browser data and retry

2. **Chart Analysis Not Working**
   - Ensure chart coordinates are set
   - Check screenshot permissions
   - Verify chart is visible and unobstructed
   - Try different time periods

3. **Trades Not Executing**
   - Check confidence threshold settings
   - Verify asset selection
   - Ensure sufficient account balance
   - Check for platform maintenance

4. **Browser Issues**
   - Update Chrome/Chromium
   - Clear browser cache
   - Disable browser extensions
   - Try Playwright fallback

### Debug Mode
Enable debug logs in Configuration for detailed troubleshooting information.

## 📝 License

MIT License - see LICENSE file for details.

## ⚠️ Disclaimer

This software is for educational purposes only. Trading involves significant risk of loss. Use at your own risk and never trade with money you cannot afford to lose. Always test with demo accounts first.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review existing GitHub issues
3. Create a new issue with detailed information
4. Include logs and configuration details
