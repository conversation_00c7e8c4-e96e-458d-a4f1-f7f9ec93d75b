appId: com.tradingbot.pocketoption
productName: Pocket Option Trading Bot
copyright: Copyright © 2024 Trading Bot Developer

directories:
  output: release
  buildResources: build

files:
  - dist/**/*
  - dist-electron/**/*
  - node_modules/**/*
  - "!node_modules/**/test/**/*"
  - "!node_modules/**/tests/**/*"
  - "!node_modules/**/*.md"
  - "!node_modules/**/*.txt"

extraResources:
  - from: "browser-data"
    to: "browser-data"
    filter:
      - "**/*"

mac:
  category: public.app-category.finance
  icon: build/icon.icns
  target:
    - target: dmg
      arch: [x64, arm64]

win:
  icon: build/icon.ico
  target:
    - target: nsis
      arch: [x64]

linux:
  icon: build/icon.png
  target:
    - target: AppImage
      arch: [x64]

nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true

publish:
  provider: generic
  url: https://releases.example.com/
